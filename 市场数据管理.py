"""
市场数据管理模块
功能：
1. 获取市场所有股票数据
2. 本地压缩缓存
3. 板块分类
4. 数据更新管理
"""

import os
import gzip
import json
import pickle
import pandas as pd
import tushare as ts
from datetime import datetime, timedelta
import threading
import time
from typing import Dict, List, Optional, Tuple
import logging
from collections import deque

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class APIRateLimiter:
    """API限流控制器"""

    def __init__(self, max_calls_per_minute: int = 450, max_records_per_call: int = 5000):
        """
        初始化API限流控制器

        Args:
            max_calls_per_minute: 每分钟最大调用次数
            max_records_per_call: 每次调用最大记录数
        """
        self.max_calls_per_minute = max_calls_per_minute
        self.max_records_per_call = max_records_per_call
        self.call_times = deque()  # 存储调用时间戳
        self.lock = threading.Lock()

        logger.info(f"API限流器初始化: 每分钟最多{max_calls_per_minute}次调用，每次最多{max_records_per_call}条记录")

    def wait_if_needed(self):
        """如果需要，等待直到可以进行下一次API调用"""
        with self.lock:
            now = time.time()

            # 清理1分钟前的调用记录
            while self.call_times and now - self.call_times[0] > 60:
                self.call_times.popleft()

            # 检查是否超过限制
            if len(self.call_times) >= self.max_calls_per_minute:
                # 计算需要等待的时间
                oldest_call = self.call_times[0]
                wait_time = 60 - (now - oldest_call) + 0.1  # 多等0.1秒确保安全

                if wait_time > 0:
                    logger.warning(f"API调用频率限制，等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)

                    # 重新清理过期记录
                    now = time.time()
                    while self.call_times and now - self.call_times[0] > 60:
                        self.call_times.popleft()

            # 记录本次调用
            self.call_times.append(now)

    def get_max_records_per_call(self) -> int:
        """获取每次调用的最大记录数"""
        return self.max_records_per_call

    def get_call_stats(self) -> Dict:
        """获取调用统计信息"""
        with self.lock:
            now = time.time()
            # 清理1分钟前的调用记录
            while self.call_times and now - self.call_times[0] > 60:
                self.call_times.popleft()

            return {
                'calls_in_last_minute': len(self.call_times),
                'max_calls_per_minute': self.max_calls_per_minute,
                'remaining_calls': max(0, self.max_calls_per_minute - len(self.call_times)),
                'max_records_per_call': self.max_records_per_call
            }

class MarketDataManager:
    """市场数据管理器"""
    
    def __init__(self, cache_dir: str = "market_data_cache", token: str = None):
        """
        初始化市场数据管理器

        Args:
            cache_dir: 缓存目录
            token: Tushare API token
        """
        self.cache_dir = cache_dir
        self.token = token
        self.pro = None

        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)

        # 初始化Tushare
        if token:
            ts.set_token(token)
            self.pro = ts.pro_api()

        # 初始化API限流器
        self.rate_limiter = APIRateLimiter(max_calls_per_minute=450, max_records_per_call=5000)

        # 实时数据缓存（内存缓存，5分钟有效）
        self.realtime_cache = {}
        self.realtime_cache_timeout = 300  # 5分钟

        # 常用股票列表（用于预加载）
        self.popular_stocks = [
            '000001.SZ', '000002.SZ', '000858.SZ', '002415.SZ', '300059.SZ',
            '600000.SH', '600036.SH', '600519.SH', '600887.SH', '000858.SZ'
        ]

        # 预加载标志
        self.preload_enabled = True
        self.preload_thread = None

        # 板块分类配置
        self.sector_config = {
            'main_board': {
                'name': '主板',
                'patterns': ['000', '001', '002', '600', '601', '603', '605']
            },
            'sme_board': {
                'name': '中小板',
                'patterns': ['002']
            },
            'gem_board': {
                'name': '创业板',
                'patterns': ['300', '301']
            },
            'star_board': {
                'name': '科创板',
                'patterns': ['688', '689']
            },
            'beijing_board': {
                'name': '北交所',
                'patterns': ['430', '831', '832', '833', '834', '835', '836', '837', '838', '839']
            }
        }
        
        # 市场分类配置
        self.market_config = {
            'SSE': {
                'name': '上海证券交易所',
                'patterns': ['600', '601', '603', '605', '688', '689']
            },
            'SZSE': {
                'name': '深圳证券交易所',
                'patterns': ['000', '001', '002', '300', '301']
            },
            'BSE': {
                'name': '北京证券交易所',
                'patterns': ['430', '831', '832', '833', '834', '835', '836', '837', '838', '839']
            }
        }
        
        # 行业分类缓存
        self.industry_cache = {}
        
        # 数据更新状态
        self.update_status = {
            'is_updating': False,
            'last_update': None,
            'progress': 0,
            'total': 0,
            'current_task': ''
        }
    
    def get_all_stock_list(self, use_cache: bool = True) -> pd.DataFrame:
        """
        获取所有股票列表
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            股票列表DataFrame
        """
        cache_file = os.path.join(self.cache_dir, 'stock_list.pkl.gz')
        
        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            try:
                cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
                if datetime.now() - cache_time < timedelta(days=1):  # 缓存1天
                    logger.info("使用缓存的股票列表")
                    return self._load_compressed_data(cache_file)
            except Exception as e:
                logger.warning(f"读取股票列表缓存失败: {e}")
        
        # 从API获取数据
        if not self.pro:
            raise ValueError("未设置Tushare API token")
        
        logger.info("从API获取股票列表...")
        try:
            # API限流控制
            self.rate_limiter.wait_if_needed()

            # 获取股票基本信息
            stock_basic = self.pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,market,list_date')

            # 添加板块分类
            stock_basic['sector'] = stock_basic['ts_code'].apply(self._classify_sector)
            stock_basic['market_name'] = stock_basic['ts_code'].apply(self._classify_market)

            # 保存到缓存（使用自适应压缩）
            self.save_data_with_adaptive_compression(stock_basic, cache_file)
            logger.info(f"获取到 {len(stock_basic)} 只股票信息")

            return stock_basic
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            raise
    
    def get_stock_daily_data(self, ts_code: str, start_date: str = None, end_date: str = None, use_cache: bool = True) -> pd.DataFrame:
        """
        获取股票日线数据（智能缓存版本）

        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            use_cache: 是否使用缓存

        Returns:
            日线数据DataFrame
        """
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')

        cache_file = os.path.join(self.cache_dir, f'daily_{ts_code}_{start_date}_{end_date}.pkl.gz')

        # 检查缓存（使用智能缓存策略）
        if use_cache and os.path.exists(cache_file):
            try:
                cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
                cache_timeout = self._get_smart_cache_timeout('daily')
                if datetime.now() - cache_time < cache_timeout:
                    logger.info(f"使用缓存的日线数据: {ts_code}")
                    return self._load_compressed_data(cache_file)
            except Exception as e:
                logger.warning(f"读取日线数据缓存失败: {e}")

        # 从API获取数据
        if not self.pro:
            raise ValueError("未设置Tushare API token")

        try:
            logger.info(f"从API获取日线数据: {ts_code}")

            # API限流控制
            self.rate_limiter.wait_if_needed()

            data = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            if not data.empty:
                data['trade_date'] = pd.to_datetime(data['trade_date'], format='%Y%m%d')
                data = data.sort_values('trade_date')

                # 保存到缓存（使用自适应压缩）
                self.save_data_with_adaptive_compression(data, cache_file)

                logger.info(f"获取日线数据成功: {len(data)}条记录")

            return data

        except Exception as e:
            logger.error(f"获取 {ts_code} 日线数据失败: {e}")
            raise
    
    def batch_update_stock_data(self, stock_codes: List[str] = None, callback=None) -> Dict:
        """
        批量更新股票数据
        
        Args:
            stock_codes: 股票代码列表，None表示更新所有股票
            callback: 进度回调函数
            
        Returns:
            更新结果统计
        """
        if self.update_status['is_updating']:
            raise RuntimeError("数据更新正在进行中")
        
        self.update_status['is_updating'] = True
        self.update_status['progress'] = 0
        
        try:
            # 获取股票列表
            if stock_codes is None:
                stock_list = self.get_all_stock_list()
                stock_codes = stock_list['ts_code'].tolist()
            
            total_stocks = len(stock_codes)
            self.update_status['total'] = total_stocks
            
            success_count = 0
            error_count = 0
            error_list = []
            
            logger.info(f"开始批量更新 {total_stocks} 只股票数据")
            
            for i, ts_code in enumerate(stock_codes):
                try:
                    self.update_status['current_task'] = f"更新 {ts_code}"
                    self.update_status['progress'] = i + 1
                    
                    # 获取最近一年的数据
                    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                    end_date = datetime.now().strftime('%Y%m%d')
                    
                    self.get_stock_daily_data(ts_code, start_date, end_date, use_cache=False)
                    success_count += 1
                    
                    # 调用回调函数
                    if callback:
                        callback(i + 1, total_stocks, ts_code, True, None)
                    
                    # 避免API限制
                    time.sleep(0.1)
                    
                except Exception as e:
                    error_count += 1
                    error_list.append((ts_code, str(e)))
                    logger.warning(f"更新 {ts_code} 失败: {e}")
                    
                    if callback:
                        callback(i + 1, total_stocks, ts_code, False, str(e))
            
            result = {
                'total': total_stocks,
                'success': success_count,
                'error': error_count,
                'error_list': error_list
            }
            
            self.update_status['last_update'] = datetime.now()
            logger.info(f"批量更新完成: 成功 {success_count}, 失败 {error_count}")
            
            return result
            
        finally:
            self.update_status['is_updating'] = False
            self.update_status['current_task'] = ''
    
    def get_sector_stocks(self, sector: str) -> List[str]:
        """
        获取指定板块的股票列表
        
        Args:
            sector: 板块名称
            
        Returns:
            股票代码列表
        """
        stock_list = self.get_all_stock_list()
        sector_stocks = stock_list[stock_list['sector'] == sector]['ts_code'].tolist()
        return sector_stocks
    
    def get_market_stocks(self, market: str) -> List[str]:
        """
        获取指定市场的股票列表
        
        Args:
            market: 市场名称
            
        Returns:
            股票代码列表
        """
        stock_list = self.get_all_stock_list()
        market_stocks = stock_list[stock_list['market_name'] == market]['ts_code'].tolist()
        return market_stocks
    
    def _classify_sector(self, ts_code: str) -> str:
        """根据股票代码分类板块"""
        code_prefix = ts_code[:3]
        
        for sector_key, sector_info in self.sector_config.items():
            if code_prefix in sector_info['patterns']:
                return sector_info['name']
        
        return '其他'
    
    def _classify_market(self, ts_code: str) -> str:
        """根据股票代码分类市场"""
        code_prefix = ts_code[:3]
        
        for market_key, market_info in self.market_config.items():
            if code_prefix in market_info['patterns']:
                return market_info['name']
        
        return '其他'

    def _is_trading_time(self) -> bool:
        """判断是否为交易时间"""
        now = datetime.now()
        weekday = now.weekday()

        # 周末不是交易时间
        if weekday >= 5:  # 5=周六, 6=周日
            return False

        # 交易时间：9:30-11:30, 13:00-15:00
        current_time = now.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()

        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)

    def _get_smart_cache_timeout(self, data_type: str) -> timedelta:
        """根据数据类型和交易时间智能调整缓存时间"""
        if data_type == 'realtime':
            if self._is_trading_time():
                return timedelta(seconds=30)  # 交易时间30秒，确保实时性
            else:
                return timedelta(minutes=30)  # 非交易时间30分钟
        elif data_type == 'daily':
            if self._is_trading_time():
                return timedelta(hours=1)    # 交易时间1小时
            else:
                return timedelta(hours=6)    # 非交易时间6小时
        elif data_type == 'stock_list':
            return timedelta(days=1)         # 股票列表1天
        elif data_type == 'industry':
            return timedelta(days=7)         # 行业数据7天
        else:
            return timedelta(hours=6)        # 默认6小时

    def _save_compressed_data(self, data: pd.DataFrame, file_path: str, compression_level: int = 6):
        """
        保存压缩数据（优化版）

        Args:
            data: 要保存的DataFrame
            file_path: 保存路径
            compression_level: 压缩级别(1-9)，默认6平衡压缩率和速度
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 先保存到临时文件，避免写入过程中断导致文件损坏
            temp_file = file_path + '.tmp'

            # 使用优化的压缩参数
            with gzip.open(temp_file, 'wb', compresslevel=compression_level) as f:
                # 使用protocol=4获得更好的性能和兼容性
                pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)

            # 原子性重命名，确保数据完整性
            if os.path.exists(temp_file):
                if os.path.exists(file_path):
                    os.remove(file_path)
                os.rename(temp_file, file_path)

                # 记录压缩效果
                file_size = os.path.getsize(file_path)
                logger.debug(f"数据已压缩保存: {file_path} ({file_size/1024:.1f}KB)")

        except Exception as e:
            # 清理临时文件
            temp_file = file_path + '.tmp'
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
            logger.error(f"保存压缩数据失败: {e}")
            raise

    def _load_compressed_data(self, file_path: str) -> pd.DataFrame:
        """
        加载压缩数据（优化版）

        Args:
            file_path: 文件路径

        Returns:
            加载的DataFrame
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"缓存文件不存在: {file_path}")

            # 检查文件大小，避免加载损坏的文件
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError(f"缓存文件为空: {file_path}")

            # 加载压缩数据
            with gzip.open(file_path, 'rb') as f:
                data = pickle.load(f)

            # 验证数据完整性
            if not isinstance(data, pd.DataFrame):
                raise ValueError(f"缓存文件格式错误: {file_path}")

            if data.empty:
                logger.warning(f"加载的数据为空: {file_path}")

            logger.debug(f"数据已从缓存加载: {file_path} ({file_size/1024:.1f}KB, {len(data)}行)")
            return data

        except (gzip.BadGzipFile, pickle.UnpicklingError) as e:
            logger.error(f"缓存文件损坏，将删除: {file_path}")
            try:
                os.remove(file_path)
            except:
                pass
            raise ValueError(f"缓存文件损坏: {e}")
        except Exception as e:
            logger.error(f"加载压缩数据失败: {e}")
            raise
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        cache_info = {
            'cache_dir': self.cache_dir,
            'total_files': 0,
            'total_size': 0,
            'files': []
        }
        
        if os.path.exists(self.cache_dir):
            for file_name in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, file_name)
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    cache_info['files'].append({
                        'name': file_name,
                        'size': file_size,
                        'modified': file_time
                    })
                    cache_info['total_files'] += 1
                    cache_info['total_size'] += file_size
        
        return cache_info
    
    def clear_cache(self, older_than_days: int = None):
        """清理缓存"""
        if not os.path.exists(self.cache_dir):
            return
        
        deleted_count = 0
        deleted_size = 0
        
        for file_name in os.listdir(self.cache_dir):
            file_path = os.path.join(self.cache_dir, file_name)
            if os.path.isfile(file_path):
                should_delete = False
                
                if older_than_days is None:
                    should_delete = True
                else:
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if datetime.now() - file_time > timedelta(days=older_than_days):
                        should_delete = True
                
                if should_delete:
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    deleted_count += 1
                    deleted_size += file_size
        
        logger.info(f"清理缓存完成: 删除 {deleted_count} 个文件, 释放 {deleted_size / 1024 / 1024:.2f} MB")
        
        return {
            'deleted_files': deleted_count,
            'deleted_size': deleted_size
        }

    def get_realtime_data(self, ts_code: str, use_cache: bool = True) -> pd.DataFrame:
        """
        获取实时数据（带缓存）

        Args:
            ts_code: 股票代码
            use_cache: 是否使用缓存

        Returns:
            实时数据DataFrame
        """
        # 检查内存缓存
        if use_cache and ts_code in self.realtime_cache:
            cache_data, cache_time = self.realtime_cache[ts_code]
            cache_timeout = self._get_smart_cache_timeout('realtime')
            if datetime.now() - cache_time < cache_timeout:
                logger.info(f"使用缓存的实时数据: {ts_code}")
                return cache_data

        # 获取今天的日期
        today = datetime.now().strftime("%Y%m%d")

        try:
            # 方法1: 尝试获取今日的日线数据
            # API限流控制
            self.rate_limiter.wait_if_needed()

            today_data = self.pro.daily(ts_code=ts_code, start_date=today, end_date=today)

            if not today_data.empty:
                today_data['trade_date'] = pd.to_datetime(today_data['trade_date'], format='%Y%m%d')
                # 缓存到内存
                self.realtime_cache[ts_code] = (today_data, datetime.now())
                logger.info(f"获取到日线实时数据: {ts_code}")
                return today_data

            # 方法2: 获取实时行情
            stock_code = ts_code.split('.')[0]
            realtime_quote = ts.get_realtime_quotes(stock_code)

            if realtime_quote is not None and not realtime_quote.empty:
                quote_row = realtime_quote.iloc[0]
                price = quote_row.get('price', '0')

                if price and price != '0' and price != '--':
                    # 转换为标准格式
                    realtime_data = self._convert_realtime_quote_to_daily(quote_row, ts_code, today)
                    # 缓存到内存
                    self.realtime_cache[ts_code] = (realtime_data, datetime.now())
                    logger.info(f"获取到实时行情数据: {ts_code}")
                    return realtime_data

            logger.warning(f"无法获取 {ts_code} 的实时数据")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return pd.DataFrame()

    def _convert_realtime_quote_to_daily(self, quote_row, ts_code: str, date: str) -> pd.DataFrame:
        """将实时行情数据转换为日线格式"""
        try:
            price_val = float(quote_row.get('price', 0))
            open_val = float(quote_row.get('open', price_val))
            high_val = float(quote_row.get('high', price_val))
            low_val = float(quote_row.get('low', price_val))
            volume = quote_row.get('volume', '1000000')
            vol_val = float(volume) if volume not in ['0', '', '--'] else 1000000

            # 数据验证和修正
            if high_val < low_val:
                high_val, low_val = low_val, high_val
            if low_val <= 0:
                low_val = min(price_val, open_val) * 0.99
            if high_val <= 0:
                high_val = max(price_val, open_val) * 1.01

            realtime_data = {
                'ts_code': ts_code,
                'trade_date': pd.to_datetime(date, format='%Y%m%d'),
                'open': open_val,
                'high': high_val,
                'low': low_val,
                'close': price_val,
                'vol': vol_val * 100,
                'amount': vol_val * price_val * 100
            }

            return pd.DataFrame([realtime_data])

        except Exception as e:
            logger.error(f"转换实时数据失败: {e}")
            return pd.DataFrame()

    def get_realtime_quotes(self, stock_codes: List[str]) -> pd.DataFrame:
        """
        获取实时行情数据

        Args:
            stock_codes: 股票代码列表

        Returns:
            实时行情DataFrame
        """
        if not self.pro:
            raise ValueError("未设置Tushare API token")

        try:
            # 转换股票代码格式（去掉后缀）
            codes = [code.split('.')[0] for code in stock_codes]

            # 获取实时行情
            quotes = []
            for code in codes:
                try:
                    quote = ts.get_realtime_quotes(code)
                    if not quote.empty:
                        quotes.append(quote.iloc[0])
                    time.sleep(0.05)  # 避免API限制
                except Exception as e:
                    logger.warning(f"获取 {code} 实时行情失败: {e}")

            if quotes:
                return pd.DataFrame(quotes)
            else:
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取实时行情失败: {e}")
            raise

    def get_industry_data(self, use_cache: bool = True) -> pd.DataFrame:
        """
        获取行业分类数据

        Args:
            use_cache: 是否使用缓存

        Returns:
            行业分类DataFrame
        """
        cache_file = os.path.join(self.cache_dir, 'industry_data.pkl.gz')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            try:
                cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
                if datetime.now() - cache_time < timedelta(days=7):  # 缓存7天
                    logger.info("使用缓存的行业数据")
                    return self._load_compressed_data(cache_file)
            except Exception as e:
                logger.warning(f"读取行业数据缓存失败: {e}")

        # 从API获取数据
        if not self.pro:
            raise ValueError("未设置Tushare API token")

        try:
            logger.info("从API获取行业数据...")

            # 获取申万行业分类
            industry_data = self.pro.index_classify(level='L1', src='SW2021')

            # 保存到缓存（使用自适应压缩）
            self.save_data_with_adaptive_compression(industry_data, cache_file)
            logger.info(f"获取到 {len(industry_data)} 个行业分类")

            return industry_data

        except Exception as e:
            logger.error(f"获取行业数据失败: {e}")
            raise

    def get_concept_data(self, use_cache: bool = True) -> pd.DataFrame:
        """
        获取概念分类数据

        Args:
            use_cache: 是否使用缓存

        Returns:
            概念分类DataFrame
        """
        cache_file = os.path.join(self.cache_dir, 'concept_data.pkl.gz')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            try:
                cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
                if datetime.now() - cache_time < timedelta(days=7):  # 缓存7天
                    return self._load_compressed_data(cache_file)
            except Exception as e:
                logger.warning(f"读取概念数据缓存失败: {e}")

        # 从API获取数据
        if not self.pro:
            raise ValueError("未设置Tushare API token")

        try:
            # 获取概念分类
            concept_data = self.pro.concept()

            # 保存到缓存（使用自适应压缩）
            self.save_data_with_adaptive_compression(concept_data, cache_file)

            return concept_data

        except Exception as e:
            logger.error(f"获取概念数据失败: {e}")
            raise

    def search_stocks(self, keyword: str, search_type: str = 'all') -> pd.DataFrame:
        """
        搜索股票

        Args:
            keyword: 搜索关键词
            search_type: 搜索类型 ('name', 'code', 'industry', 'all')

        Returns:
            搜索结果DataFrame
        """
        stock_list = self.get_all_stock_list()

        if search_type == 'name':
            result = stock_list[stock_list['name'].str.contains(keyword, na=False)]
        elif search_type == 'code':
            result = stock_list[stock_list['ts_code'].str.contains(keyword, na=False) |
                              stock_list['symbol'].str.contains(keyword, na=False)]
        elif search_type == 'industry':
            result = stock_list[stock_list['industry'].str.contains(keyword, na=False)]
        else:  # all
            result = stock_list[
                stock_list['name'].str.contains(keyword, na=False) |
                stock_list['ts_code'].str.contains(keyword, na=False) |
                stock_list['symbol'].str.contains(keyword, na=False) |
                stock_list['industry'].str.contains(keyword, na=False)
            ]

        return result

    def get_stock_info(self, ts_code: str) -> Dict:
        """
        获取股票详细信息

        Args:
            ts_code: 股票代码

        Returns:
            股票信息字典
        """
        stock_list = self.get_all_stock_list()
        stock_info = stock_list[stock_list['ts_code'] == ts_code]

        if stock_info.empty:
            return {}

        info = stock_info.iloc[0].to_dict()

        # 添加更多信息
        try:
            # 获取股票基本面数据
            if self.pro:
                # 获取最新财务数据
                fina_data = self.pro.fina_indicator(ts_code=ts_code, limit=1)
                if not fina_data.empty:
                    info['pe_ratio'] = fina_data.iloc[0].get('pe', None)
                    info['pb_ratio'] = fina_data.iloc[0].get('pb', None)
                    info['roe'] = fina_data.iloc[0].get('roe', None)

                # 获取股本信息
                share_data = self.pro.share_float(ts_code=ts_code, limit=1)
                if not share_data.empty:
                    info['total_share'] = share_data.iloc[0].get('total_share', None)
                    info['float_share'] = share_data.iloc[0].get('float_share', None)

        except Exception as e:
            logger.warning(f"获取 {ts_code} 详细信息失败: {e}")

        return info

    def get_update_status(self) -> Dict:
        """获取数据更新状态"""
        return self.update_status.copy()

    def stop_update(self):
        """停止数据更新"""
        self.update_status['is_updating'] = False

    def export_stock_list(self, file_path: str, format: str = 'excel'):
        """
        导出股票列表

        Args:
            file_path: 导出文件路径
            format: 导出格式 ('excel', 'csv', 'json')
        """
        stock_list = self.get_all_stock_list()

        if format == 'excel':
            stock_list.to_excel(file_path, index=False)
        elif format == 'csv':
            stock_list.to_csv(file_path, index=False, encoding='utf-8-sig')
        elif format == 'json':
            stock_list.to_json(file_path, orient='records', force_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

        logger.info(f"股票列表已导出到: {file_path}")

    def get_sector_summary(self) -> Dict:
        """获取板块汇总信息"""
        stock_list = self.get_all_stock_list()

        summary = {}
        for sector in stock_list['sector'].unique():
            sector_stocks = stock_list[stock_list['sector'] == sector]
            summary[sector] = {
                'count': len(sector_stocks),
                'stocks': sector_stocks['ts_code'].tolist()
            }

        return summary

    def get_market_summary(self) -> Dict:
        """获取市场汇总信息"""
        stock_list = self.get_all_stock_list()

        summary = {}
        for market in stock_list['market_name'].unique():
            market_stocks = stock_list[stock_list['market_name'] == market]
            summary[market] = {
                'count': len(market_stocks),
                'stocks': market_stocks['ts_code'].tolist()
            }

        return summary

    def start_preload(self):
        """启动数据预加载"""
        if not self.preload_enabled or self.preload_thread is not None:
            return

        def preload_worker():
            """预加载工作线程"""
            logger.info("开始预加载常用股票数据...")

            for ts_code in self.popular_stocks:
                try:
                    if not self.preload_enabled:
                        break

                    # 预加载日线数据（最近3个月）
                    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
                    end_date = datetime.now().strftime('%Y%m%d')

                    # 检查是否已有缓存
                    cache_file = os.path.join(self.cache_dir, f'daily_{ts_code}_{start_date}_{end_date}.pkl.gz')
                    if not os.path.exists(cache_file):
                        self.get_stock_daily_data(ts_code, start_date, end_date, use_cache=False)
                        logger.info(f"预加载完成: {ts_code}")

                    # 避免API限制
                    time.sleep(0.2)

                except Exception as e:
                    logger.warning(f"预加载 {ts_code} 失败: {e}")

            logger.info("数据预加载完成")

        self.preload_thread = threading.Thread(target=preload_worker, daemon=True)
        self.preload_thread.start()

    def stop_preload(self):
        """停止数据预加载"""
        self.preload_enabled = False
        if self.preload_thread and self.preload_thread.is_alive():
            self.preload_thread.join(timeout=1)
        self.preload_thread = None

    def add_popular_stock(self, ts_code: str):
        """添加常用股票到预加载列表"""
        if ts_code not in self.popular_stocks:
            self.popular_stocks.append(ts_code)
            logger.info(f"添加常用股票: {ts_code}")

    def remove_popular_stock(self, ts_code: str):
        """从预加载列表移除股票"""
        if ts_code in self.popular_stocks:
            self.popular_stocks.remove(ts_code)
            logger.info(f"移除常用股票: {ts_code}")

    def clear_realtime_cache(self):
        """清理实时数据缓存"""
        self.realtime_cache.clear()
        logger.info("实时数据缓存已清理")

    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        cache_info = self.get_cache_info()

        stats = {
            'disk_cache': cache_info,
            'memory_cache': {
                'realtime_items': len(self.realtime_cache),
                'popular_stocks': len(self.popular_stocks),
                'preload_running': self.preload_thread is not None and self.preload_thread.is_alive()
            }
        }

        return stats

    def get_batch_stock_data(self, stock_codes: List[str], start_date: str, end_date: str,
                           use_cache: bool = True, batch_size: int = None) -> Dict[str, pd.DataFrame]:
        """
        批量获取股票数据（支持API限流）

        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            use_cache: 是否使用缓存
            batch_size: 批次大小，None时自动计算

        Returns:
            股票代码到数据的字典
        """
        if not stock_codes:
            return {}

        # 自动计算批次大小
        if batch_size is None:
            # 根据API限制计算最优批次大小
            max_records = self.rate_limiter.get_max_records_per_call()
            # 假设每只股票平均250条记录（一年交易日）
            estimated_records_per_stock = 250
            batch_size = max(1, max_records // estimated_records_per_stock)

        logger.info(f"开始批量获取{len(stock_codes)}只股票数据，批次大小: {batch_size}")

        results = {}
        failed_stocks = []

        # 分批处理
        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(stock_codes) + batch_size - 1) // batch_size

            logger.info(f"处理第 {batch_num}/{total_batches} 批，股票数: {len(batch_codes)}")

            # 获取API调用统计
            stats = self.rate_limiter.get_call_stats()
            logger.info(f"API调用状态: {stats['calls_in_last_minute']}/{stats['max_calls_per_minute']}")

            for stock_code in batch_codes:
                try:
                    data = self.get_stock_daily_data(stock_code, start_date, end_date, use_cache)
                    if not data.empty:
                        results[stock_code] = data
                    else:
                        logger.warning(f"股票 {stock_code} 无数据")

                except Exception as e:
                    logger.error(f"获取股票 {stock_code} 数据失败: {e}")
                    failed_stocks.append(stock_code)

            # 批次间短暂休息
            if i + batch_size < len(stock_codes):
                time.sleep(0.1)

        logger.info(f"批量获取完成: 成功{len(results)}只，失败{len(failed_stocks)}只")

        if failed_stocks:
            logger.warning(f"失败的股票: {failed_stocks[:10]}{'...' if len(failed_stocks) > 10 else ''}")

        return results

    def get_api_usage_stats(self) -> Dict:
        """获取API使用统计"""
        stats = self.rate_limiter.get_call_stats()

        return {
            'rate_limiter': stats,
            'cache_stats': self.get_cache_stats(),
            'recommendations': self._get_usage_recommendations(stats)
        }

    def _get_usage_recommendations(self, stats: Dict) -> List[str]:
        """根据API使用情况给出建议"""
        recommendations = []

        remaining_calls = stats['remaining_calls']
        max_calls = stats['max_calls_per_minute']
        usage_rate = (max_calls - remaining_calls) / max_calls

        if usage_rate > 0.9:
            recommendations.append("⚠️ API调用频率接近限制，建议增加缓存使用")
        elif usage_rate > 0.7:
            recommendations.append("🔶 API调用频率较高，建议适当控制请求频率")
        else:
            recommendations.append("✅ API调用频率正常")

        if remaining_calls < 50:
            recommendations.append("⏰ 剩余调用次数较少，建议等待或使用缓存")

        recommendations.append(f"💡 建议批次大小: {max(1, stats['max_records_per_call'] // 250)}")

        return recommendations

    def analyze_compression_efficiency(self) -> Dict:
        """
        分析压缩效率

        Returns:
            压缩效率统计信息
        """
        if not os.path.exists(self.cache_dir):
            return {'error': '缓存目录不存在'}

        compression_stats = {
            'total_files': 0,
            'total_compressed_size': 0,
            'estimated_uncompressed_size': 0,
            'compression_ratio': 0,
            'space_saved': 0,
            'files_analysis': []
        }

        try:
            for file_name in os.listdir(self.cache_dir):
                if not file_name.endswith('.pkl.gz'):
                    continue

                file_path = os.path.join(self.cache_dir, file_name)
                if not os.path.isfile(file_path):
                    continue

                compressed_size = os.path.getsize(file_path)

                # 估算未压缩大小（通过临时解压缩一小部分）
                try:
                    with gzip.open(file_path, 'rb') as f:
                        # 读取文件头来估算未压缩大小
                        sample_data = f.read(1024)  # 读取1KB样本
                        f.seek(0)

                        # 加载完整数据来获取准确的未压缩大小
                        data = pickle.load(f)

                        # 估算未压缩大小（DataFrame的内存占用）
                        uncompressed_size = data.memory_usage(deep=True).sum()

                        file_compression_ratio = compressed_size / uncompressed_size if uncompressed_size > 0 else 1
                        space_saved = uncompressed_size - compressed_size

                        compression_stats['files_analysis'].append({
                            'file': file_name,
                            'compressed_size': compressed_size,
                            'uncompressed_size': uncompressed_size,
                            'compression_ratio': file_compression_ratio,
                            'space_saved': space_saved,
                            'rows': len(data),
                            'columns': len(data.columns) if hasattr(data, 'columns') else 0
                        })

                        compression_stats['total_compressed_size'] += compressed_size
                        compression_stats['estimated_uncompressed_size'] += uncompressed_size
                        compression_stats['total_files'] += 1

                except Exception as e:
                    logger.warning(f"分析文件 {file_name} 压缩效率失败: {e}")
                    continue

            # 计算总体压缩统计
            if compression_stats['estimated_uncompressed_size'] > 0:
                compression_stats['compression_ratio'] = (
                    compression_stats['total_compressed_size'] /
                    compression_stats['estimated_uncompressed_size']
                )
                compression_stats['space_saved'] = (
                    compression_stats['estimated_uncompressed_size'] -
                    compression_stats['total_compressed_size']
                )

            return compression_stats

        except Exception as e:
            logger.error(f"分析压缩效率失败: {e}")
            return {'error': str(e)}

    def optimize_compression_settings(self, data_size_mb: float) -> int:
        """
        根据数据大小优化压缩设置

        Args:
            data_size_mb: 数据大小(MB)

        Returns:
            推荐的压缩级别
        """
        if data_size_mb < 1:
            # 小文件使用高压缩比
            return 9
        elif data_size_mb < 10:
            # 中等文件平衡压缩比和速度
            return 6
        else:
            # 大文件优先速度
            return 3

    def save_data_with_adaptive_compression(self, data: pd.DataFrame, file_path: str):
        """
        使用自适应压缩保存数据

        Args:
            data: 要保存的DataFrame
            file_path: 保存路径
        """
        # 估算数据大小
        estimated_size_mb = data.memory_usage(deep=True).sum() / 1024 / 1024

        # 选择最优压缩级别
        compression_level = self.optimize_compression_settings(estimated_size_mb)

        logger.debug(f"数据大小: {estimated_size_mb:.2f}MB, 使用压缩级别: {compression_level}")

        # 保存数据
        self._save_compressed_data(data, file_path, compression_level)

    def get_index_components(self, index_code: str, use_cache: bool = True) -> List[str]:
        """
        获取指数成分股

        Args:
            index_code: 指数代码 (如 '000852.SH' 中证1000)
            use_cache: 是否使用缓存

        Returns:
            成分股代码列表
        """
        cache_file = os.path.join(self.cache_dir, f'index_components_{index_code.replace(".", "_")}.pkl.gz')

        # 检查缓存
        if use_cache and os.path.exists(cache_file):
            try:
                cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file))
                if datetime.now() - cache_time < timedelta(days=7):  # 缓存7天
                    logger.info(f"使用缓存的指数成分股: {index_code}")
                    components_data = self._load_compressed_data(cache_file)
                    return components_data['con_code'].tolist() if not components_data.empty else []
            except Exception as e:
                logger.warning(f"读取指数成分股缓存失败: {e}")

        # 从API获取数据
        if not self.pro:
            logger.warning("未设置Tushare API token，使用模拟数据")
            return self._get_simulated_index_components(index_code)

        try:
            logger.info(f"从API获取指数成分股: {index_code}")

            # API限流控制
            self.rate_limiter.wait_if_needed()

            # 获取指数成分股
            components = self.pro.index_weight(index_code=index_code, trade_date='')

            if not components.empty:
                # 保存到缓存
                self.save_data_with_adaptive_compression(components, cache_file)
                logger.info(f"获取到 {len(components)} 只成分股")
                return components['con_code'].tolist()
            else:
                logger.warning(f"未获取到指数 {index_code} 的成分股，使用模拟数据")
                return self._get_simulated_index_components(index_code)

        except Exception as e:
            logger.error(f"获取指数成分股失败: {e}，使用模拟数据")
            return self._get_simulated_index_components(index_code)

    def _get_simulated_index_components(self, index_code: str) -> List[str]:
        """
        获取模拟的指数成分股（当API不可用时使用）

        Args:
            index_code: 指数代码

        Returns:
            模拟的成分股代码列表
        """
        try:
            stock_list = self.get_all_stock_list()

            if index_code == '000852.SH':  # 中证1000
                # 中证1000主要包含中小盘股票
                filtered_stocks = stock_list[
                    (stock_list['sector'].isin(['主板', '中小板'])) |
                    ((stock_list['sector'] == '创业板') & (stock_list['ts_code'] < '300800')) |
                    ((stock_list['sector'] == '科创板') & (stock_list['ts_code'] < '688300'))
                ]

                # 排除沪深300的大盘股（简单模拟）
                excluded_prefixes = ['600000', '600036', '600519', '000001', '000002', '000858']
                for prefix in excluded_prefixes:
                    filtered_stocks = filtered_stocks[~filtered_stocks['ts_code'].str.startswith(prefix)]

                all_codes = filtered_stocks['ts_code'].tolist()
                import random
                random.seed(42)  # 固定种子确保结果一致
                return random.sample(all_codes, min(1000, len(all_codes)))

            elif index_code == '000300.SH':  # 沪深300
                # 选择主要的大盘股
                hs300_codes = []

                # 上证主要股票
                sh_stocks = stock_list[stock_list['ts_code'].str.startswith('60')]
                hs300_codes.extend(sh_stocks['ts_code'].head(150).tolist())

                # 深证主要股票
                sz_stocks = stock_list[stock_list['ts_code'].str.startswith('00')]
                hs300_codes.extend(sz_stocks['ts_code'].head(150).tolist())

                return hs300_codes[:300]

            elif index_code == '000016.SH':  # 上证50
                # 选择上证的主要大盘股
                sh_stocks = stock_list[stock_list['ts_code'].str.startswith('60')]
                return sh_stocks['ts_code'].head(50).tolist()

            else:
                logger.warning(f"未知的指数代码: {index_code}")
                return []

        except Exception as e:
            logger.error(f"生成模拟指数成分股失败: {e}")
            return []

    def get_sector_stocks_enhanced(self, sector_type: str) -> List[str]:
        """
        增强版板块股票获取

        Args:
            sector_type: 板块类型

        Returns:
            股票代码列表
        """
        try:
            if sector_type == "全部股票":
                stock_list = self.get_all_stock_list()
                return stock_list['ts_code'].tolist()

            elif sector_type in ["主板", "中小板", "创业板", "科创板", "北交所"]:
                stock_list = self.get_all_stock_list()
                sector_stocks = stock_list[stock_list['sector'] == sector_type]
                return sector_stocks['ts_code'].tolist()

            elif sector_type == "中证1000":
                return self.get_index_components('000852.SH')

            elif sector_type == "沪深300":
                return self.get_index_components('000300.SH')

            elif sector_type == "上证50":
                return self.get_index_components('000016.SH')

            else:
                logger.warning(f"不支持的板块类型: {sector_type}")
                return []

        except Exception as e:
            logger.error(f"获取板块股票失败: {e}")
            return []
