#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票看图软件打包脚本
使用PyInstaller将Python程序打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file():
    """创建PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
added_files = [
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('策略示例', '策略示例'),
]

a = Analysis(
    ['股票看图软件_增强版.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tushare',
        'pandas',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'numpy',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.edge.options',
        'openpyxl',
        'xlrd',
        'requests',
        'lxml',
        'bs4',
        'datetime',
        'threading',
        'json',
        'time',
        'importlib',
        'importlib.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='股票看图软件_增强版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('股票看图软件_增强版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建PyInstaller配置文件: 股票看图软件_增强版.spec")

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "股票看图软件_增强版.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ exe文件构建成功！")
            print(f"输出目录: {os.path.abspath('dist')}")
            return True
        else:
            print("❌ exe文件构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现异常: {str(e)}")
        return False

def copy_additional_files():
    """复制额外需要的文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 需要复制的文件列表
    files_to_copy = [
        "requirements.txt",
        "README.md",
        "东方财富交易界面源码.txt",
        "东方财富持仓网页.txt", 
        "东方财富登录源代码.txt",
        "东方财富账户分析.txt",
    ]
    
    # 需要复制的目录
    dirs_to_copy = [
        "策略示例",
    ]
    
    print("正在复制额外文件...")
    
    # 复制文件
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"✓ 已复制: {file_name}")
            except Exception as e:
                print(f"⚠ 复制文件失败 {file_name}: {str(e)}")
    
    # 复制目录
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            try:
                dest_dir = dist_dir / dir_name
                if dest_dir.exists():
                    shutil.rmtree(dest_dir)
                shutil.copytree(dir_name, dest_dir)
                print(f"✓ 已复制目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 复制目录失败 {dir_name}: {str(e)}")
    
    return True

def create_readme():
    """创建README文件"""
    readme_content = """# 股票看图软件 - 增强版

## 软件介绍
这是一个功能强大的股票分析和量化交易软件，包含以下主要功能：

### 主要功能
1. **股票分析**
   - 支持MACD、KDJ、缠论等技术指标
   - 实时K线图显示
   - 十字光标和导航功能

2. **策略回测**
   - 内置多种经典策略
   - 自定义策略编写
   - 详细的回测分析报告

3. **多股票回测**
   - 批量回测多只股票
   - 策略效果对比分析
   - 最优参数寻找

4. **网页交易**
   - 自动化交易执行
   - 智能持仓管理
   - 基于策略信号的交易决策

## 使用说明

### 首次使用
1. 双击运行 `股票看图软件_增强版.exe`
2. 在股票分析页面输入股票代码（如：000001.SZ）
3. 点击"查询"获取股票数据
4. 选择技术指标进行分析

### 策略回测
1. 切换到"策略回测"页面
2. 设置回测参数（股票代码、日期范围、初始资金等）
3. 选择策略类型（MACD、KDJ或自定义）
4. 点击"运行回测"查看结果

### 网页交易
1. 切换到"网页交易"页面
2. 点击"连接交易网站"
3. 在浏览器中完成登录
4. 设置交易参数和仓位管理
5. 启用自动交易功能

## 注意事项
1. 首次运行可能需要较长时间加载
2. 网页交易功能需要安装Edge浏览器
3. 请确保网络连接正常
4. 自动交易涉及真实资金，请谨慎使用

## 技术支持
如有问题请联系技术支持。

## 版本信息
版本: 增强版 v1.0
构建日期: """ + str(datetime.now().strftime("%Y-%m-%d")) + """
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 已创建README.md文件")

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_clean = ['build', '__pycache__']
    files_to_clean = ['*.pyc']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 清理目录失败 {dir_name}: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("股票看图软件 - 打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('股票看图软件_增强版.py'):
        print("❌ 未找到主程序文件: 股票看图软件_增强版.py")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建README文件
    create_readme()
    
    # 创建spec配置文件
    create_spec_file()
    
    # 构建exe文件
    if not build_exe():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "=" * 60)
    print("🎉 打包完成！")
    print("=" * 60)
    print(f"exe文件位置: {os.path.abspath('dist/股票看图软件_增强版.exe')}")
    print(f"完整程序目录: {os.path.abspath('dist')}")
    print("\n使用说明:")
    print("1. 进入dist目录")
    print("2. 双击运行 股票看图软件_增强版.exe")
    print("3. 首次运行可能需要较长时间加载")
    print("\n注意事项:")
    print("- 请确保目标电脑已安装Edge浏览器（用于网页交易）")
    print("- 建议将整个dist目录复制到目标电脑")
    print("- 如遇到问题，请查看README.md文件")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
