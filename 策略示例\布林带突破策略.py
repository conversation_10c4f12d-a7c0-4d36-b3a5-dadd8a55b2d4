# 布林带突破策略
# 价格突破布林带上下轨时的交易策略

signals = [0] * len(data)

# 参数设置
bb_period = 20     # 布林带周期
bb_std = 2.0       # 标准差倍数

# 计算布林带
upper, middle, lower = BOLL(data['close'], bb_period, bb_std)

for i in range(1, len(data)):
    if (pd.notna(upper.iloc[i]) and pd.notna(lower.iloc[i]) and 
        pd.notna(upper.iloc[i-1]) and pd.notna(lower.iloc[i-1])):
        
        # 价格从下方突破下轨，买入信号
        if (data['close'].iloc[i] > lower.iloc[i] and 
            data['close'].iloc[i-1] <= lower.iloc[i-1]):
            signals[i] = 1
        
        # 价格从上方跌破上轨，卖出信号
        elif (data['close'].iloc[i] < upper.iloc[i] and 
              data['close'].iloc[i-1] >= upper.iloc[i-1]):
            signals[i] = -1
