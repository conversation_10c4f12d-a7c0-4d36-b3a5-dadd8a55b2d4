#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页交易界面优化演示
展示带滚动条的网页交易界面设计
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import time

class WebTradingUIDemo:
    """网页交易界面优化演示类"""
    
    def __init__(self, master):
        self.master = master
        master.title("网页交易界面优化演示")
        master.geometry("1000x700")
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(self.master, text="网页交易界面优化演示", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # 主框架
        main_frame = tk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 左侧配置区域 - 使用Canvas和Scrollbar实现滚动
        left_canvas_frame = tk.Frame(main_frame, relief=tk.SUNKEN, bd=1)
        left_canvas_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 创建Canvas和滚动条
        self.left_canvas = tk.Canvas(left_canvas_frame, width=320, highlightthickness=0, bg='white')
        left_scrollbar = tk.Scrollbar(left_canvas_frame, orient="vertical", command=self.left_canvas.yview)
        self.scrollable_left_frame = tk.Frame(self.left_canvas, bg='white')
        
        # 配置滚动
        self.scrollable_left_frame.bind(
            "<Configure>",
            lambda e: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
        )
        
        self.left_canvas.create_window((0, 0), window=self.scrollable_left_frame, anchor="nw")
        self.left_canvas.configure(yscrollcommand=left_scrollbar.set)
        
        # 布局Canvas和滚动条
        self.left_canvas.pack(side="left", fill="both", expand=True)
        left_scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            self.left_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        self.left_canvas.bind("<MouseWheel>", _on_mousewheel)
        
        # 现在使用scrollable_left_frame作为左侧内容的父容器
        left_frame = self.scrollable_left_frame
        
        # 滚动提示
        scroll_tip = tk.Label(left_frame, text="🖱️ 鼠标滚轮可滚动查看所有内容", 
                             fg='blue', font=('Arial', 9), bg='white')
        scroll_tip.pack(pady=5)
        
        # 交易账户配置
        account_frame = tk.LabelFrame(left_frame, text="交易账户配置", padx=10, pady=10)
        account_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        tk.Label(account_frame, text="账户:").grid(row=0, column=0, sticky='w', pady=2)
        self.account_entry = tk.Entry(account_frame, width=20, show='*')
        self.account_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        
        tk.Label(account_frame, text="密码:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_entry = tk.Entry(account_frame, width=20, show='*')
        self.password_entry.grid(row=1, column=1, pady=2, padx=(5, 0))
        
        # 交易参数配置
        trading_frame = tk.LabelFrame(left_frame, text="交易参数", padx=10, pady=10)
        trading_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        tk.Label(trading_frame, text="股票代码:").grid(row=0, column=0, sticky='w', pady=2)
        self.trade_code_entry = tk.Entry(trading_frame, width=20)
        self.trade_code_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        self.trade_code_entry.insert(0, "000001")
        
        tk.Label(trading_frame, text="交易数量:").grid(row=1, column=0, sticky='w', pady=2)
        self.trade_quantity_entry = tk.Entry(trading_frame, width=20)
        self.trade_quantity_entry.grid(row=1, column=1, pady=2, padx=(5, 0))
        self.trade_quantity_entry.insert(0, "100")
        
        tk.Label(trading_frame, text="价格类型:").grid(row=2, column=0, sticky='w', pady=2)
        self.price_type_var = tk.StringVar(value="market")
        price_frame = tk.Frame(trading_frame)
        price_frame.grid(row=2, column=1, sticky='w', pady=2, padx=(5, 0))
        tk.Radiobutton(price_frame, text="市价", variable=self.price_type_var, value="market").pack(side=tk.LEFT)
        tk.Radiobutton(price_frame, text="限价", variable=self.price_type_var, value="limit").pack(side=tk.LEFT)
        
        tk.Label(trading_frame, text="限价价格:").grid(row=3, column=0, sticky='w', pady=2)
        self.limit_price_entry = tk.Entry(trading_frame, width=20)
        self.limit_price_entry.grid(row=3, column=1, pady=2, padx=(5, 0))
        
        # 仓位管理配置
        position_frame = tk.LabelFrame(left_frame, text="仓位管理", padx=10, pady=10)
        position_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        tk.Label(position_frame, text="交易金额:").grid(row=0, column=0, sticky='w', pady=2)
        self.trade_amount_entry = tk.Entry(position_frame, width=20)
        self.trade_amount_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        self.trade_amount_entry.insert(0, "10000")
        
        tk.Label(position_frame, text="仓位比例:").grid(row=1, column=0, sticky='w', pady=2)
        self.position_ratio_var = tk.StringVar(value="0.5")
        ratio_frame = tk.Frame(position_frame)
        ratio_frame.grid(row=1, column=1, sticky='w', pady=2, padx=(5, 0))
        tk.Radiobutton(ratio_frame, text="25%", variable=self.position_ratio_var, value="0.25").pack(side=tk.LEFT)
        tk.Radiobutton(ratio_frame, text="50%", variable=self.position_ratio_var, value="0.5").pack(side=tk.LEFT)
        tk.Radiobutton(ratio_frame, text="75%", variable=self.position_ratio_var, value="0.75").pack(side=tk.LEFT)
        tk.Radiobutton(ratio_frame, text="100%", variable=self.position_ratio_var, value="1.0").pack(side=tk.LEFT)
        
        # 持仓信息显示
        holdings_frame = tk.LabelFrame(left_frame, text="持仓信息", padx=10, pady=10)
        holdings_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        self.current_holdings_label = tk.Label(holdings_frame, text="当前持股: 500股 (平安银行)", fg='green')
        self.current_holdings_label.pack(anchor='w')
        
        self.available_funds_label = tk.Label(holdings_frame, text="可用资金: ¥45,000.00", fg='green')
        self.available_funds_label.pack(anchor='w')
        
        # 查询按钮
        query_button_frame = tk.Frame(holdings_frame)
        query_button_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.query_holdings_button = tk.Button(query_button_frame, text="查询持仓",
                                              command=self.demo_query_holdings,
                                              bg='lightcyan', width=10)
        self.query_holdings_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.query_funds_button = tk.Button(query_button_frame, text="查询资金",
                                           command=self.demo_query_funds,
                                           bg='lightcyan', width=10)
        self.query_funds_button.pack(side=tk.LEFT)
        
        # 策略交易配置
        strategy_trading_frame = tk.LabelFrame(left_frame, text="策略交易", padx=10, pady=10)
        strategy_trading_frame.pack(fill=tk.X, pady=(0, 10), padx=5)
        
        self.auto_trading_var = tk.BooleanVar(value=False)
        auto_trading_checkbox = tk.Checkbutton(strategy_trading_frame, text="启用自动交易",
                                              variable=self.auto_trading_var,
                                              command=self.toggle_auto_trading)
        auto_trading_checkbox.pack(anchor='w')
        
        # 添加说明文字
        auto_trading_note = tk.Label(strategy_trading_frame,
                                   text="注意：勾选后需要点击'开始自动交易'按钮才会开始监控",
                                   fg='gray', font=('Arial', 9))
        auto_trading_note.pack(anchor='w', pady=(2, 0))
        
        # 创建一个子框架来使用grid布局
        interval_frame = tk.Frame(strategy_trading_frame)
        interval_frame.pack(fill=tk.X, pady=(5, 0))
        
        tk.Label(interval_frame, text="监控间隔(秒):").grid(row=0, column=0, sticky='w', pady=2)
        self.monitor_interval_entry = tk.Entry(interval_frame, width=10)
        self.monitor_interval_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        self.monitor_interval_entry.insert(0, "60")
        
        # 操作按钮
        button_frame = tk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0), padx=5)
        
        self.connect_button = tk.Button(button_frame, text="连接交易网站",
                                       command=self.demo_connect,
                                       bg='lightblue')
        self.connect_button.pack(fill=tk.X, pady=2)
        
        self.manual_buy_button = tk.Button(button_frame, text="手动买入",
                                          command=self.demo_buy,
                                          bg='lightgreen')
        self.manual_buy_button.pack(fill=tk.X, pady=2)
        
        self.manual_sell_button = tk.Button(button_frame, text="手动卖出",
                                           command=self.demo_sell,
                                           bg='lightcoral')
        self.manual_sell_button.pack(fill=tk.X, pady=2)
        
        self.start_auto_button = tk.Button(button_frame, text="开始自动交易",
                                          command=self.demo_start_auto,
                                          bg='orange')
        self.start_auto_button.pack(fill=tk.X, pady=2)
        
        self.stop_auto_button = tk.Button(button_frame, text="停止自动交易",
                                         command=self.demo_stop_auto,
                                         bg='red', state='disabled')
        self.stop_auto_button.pack(fill=tk.X, pady=2)
        
        # 添加更多演示按钮
        demo_frame = tk.LabelFrame(left_frame, text="演示功能", padx=10, pady=10)
        demo_frame.pack(fill=tk.X, pady=(10, 0), padx=5)
        
        demo_buttons = [
            ("测试按钮1", 'lightgray'),
            ("测试按钮2", 'lightgray'),
            ("测试按钮3", 'lightgray'),
            ("测试按钮4", 'lightgray'),
            ("测试按钮5", 'lightgray'),
        ]
        
        for text, color in demo_buttons:
            btn = tk.Button(demo_frame, text=text, bg=color, command=lambda t=text: self.demo_action(t))
            btn.pack(fill=tk.X, pady=1)
        
        # 底部说明
        bottom_note = tk.Label(left_frame, 
                              text="✅ 所有按钮都可以正常点击\n🖱️ 使用鼠标滚轮上下滚动",
                              fg='green', font=('Arial', 9), justify=tk.LEFT)
        bottom_note.pack(pady=10)
        
        # 右侧日志和状态区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 界面说明
        info_frame = tk.LabelFrame(right_frame, text="界面优化说明", padx=10, pady=5)
        info_frame.pack(fill=tk.X, pady=(0, 5))
        
        info_text = tk.Text(info_frame, height=6, width=50)
        info_text.pack(fill=tk.X)
        
        info_content = """✅ 问题解决：
• 左侧配置区域添加了滚动条功能
• 支持鼠标滚轮上下滚动
• 所有按钮都可以正常访问和点击
• 界面布局更加合理和美观

🖱️ 使用方法：
• 在左侧区域使用鼠标滚轮滚动
• 可以看到所有配置选项和操作按钮
• 不再有按钮被遮挡的问题"""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state='disabled')
        
        # 状态显示
        status_frame = tk.LabelFrame(right_frame, text="交易状态", padx=10, pady=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.connection_status_label = tk.Label(status_frame, text="连接状态: 演示模式", fg='blue')
        self.connection_status_label.pack(anchor='w')
        
        self.auto_trading_status_label = tk.Label(status_frame, text="自动交易: 未启动", fg='gray')
        self.auto_trading_status_label.pack(anchor='w')
        
        self.last_signal_label = tk.Label(status_frame, text="最新信号: 演示信号", fg='blue')
        self.last_signal_label.pack(anchor='w')
        
        # 操作日志
        log_frame = tk.LabelFrame(right_frame, text="操作日志", padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=50)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 初始化日志
        self.log_message("网页交易界面优化演示已启动")
        self.log_message("✅ 左侧区域支持鼠标滚轮滚动")
        self.log_message("✅ 所有按钮都可以正常点击")
        self.log_message("🖱️ 请在左侧区域尝试鼠标滚轮滚动")
        
    def log_message(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.master.update()
    
    def demo_connect(self):
        self.log_message("🔗 演示：连接交易网站")
        self.connection_status_label.config(text="连接状态: 已连接", fg='green')
    
    def demo_buy(self):
        stock_code = self.trade_code_entry.get()
        quantity = self.trade_quantity_entry.get()
        self.log_message(f"📈 演示：手动买入 {stock_code} {quantity}股")
    
    def demo_sell(self):
        stock_code = self.trade_code_entry.get()
        quantity = self.trade_quantity_entry.get()
        self.log_message(f"📉 演示：手动卖出 {stock_code} {quantity}股")
    
    def demo_start_auto(self):
        self.log_message("🤖 演示：开始自动交易")
        self.auto_trading_status_label.config(text="自动交易: 运行中", fg='green')
        self.start_auto_button.config(state='disabled')
        self.stop_auto_button.config(state='normal')
    
    def demo_stop_auto(self):
        self.log_message("⏹️ 演示：停止自动交易")
        self.auto_trading_status_label.config(text="自动交易: 已停止", fg='red')
        self.start_auto_button.config(state='normal')
        self.stop_auto_button.config(state='disabled')
    
    def demo_query_holdings(self):
        self.log_message("📊 演示：查询持仓信息")
        self.current_holdings_label.config(text="当前持股: 500股 (平安银行)", fg='green')
    
    def demo_query_funds(self):
        self.log_message("💰 演示：查询资金信息")
        self.available_funds_label.config(text="可用资金: ¥45,000.00", fg='green')
    
    def toggle_auto_trading(self):
        if self.auto_trading_var.get():
            self.log_message("✅ 启用自动交易（需要点击开始按钮）")
        else:
            self.log_message("❌ 禁用自动交易")
    
    def demo_action(self, button_name):
        self.log_message(f"🔘 点击了 {button_name}")

def main():
    """主函数"""
    print("启动网页交易界面优化演示...")
    
    root = tk.Tk()
    app = WebTradingUIDemo(root)
    
    print("✅ 演示程序已启动")
    print("🖱️ 请在左侧区域尝试鼠标滚轮滚动")
    
    root.mainloop()

if __name__ == "__main__":
    main()
