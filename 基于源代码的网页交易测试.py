#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于源代码的网页交易测试
使用从源代码中提取的准确元素定位进行测试
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import time

# 导入selenium相关库
try:
    from selenium import webdriver
    from selenium.webdriver.edge.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.edge.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class SourceCodeBasedTradingTest:
    """基于源代码的网页交易测试类"""
    
    def __init__(self, master):
        self.master = master
        master.title("基于源代码的网页交易测试")
        master.geometry("1000x700")
        
        # 初始化变量
        self.web_driver = None
        self.is_connected = False
        
        # 从源代码中提取的元素定位信息
        self.elements = {
            'login': {
                'account_input': 'txtZjzh',  # 资金账号输入框
                'password_input': 'txtSec',  # 密码输入框
                'captcha_input': 'txtValidCode',  # 验证码输入框
                'login_button': 'btnConfirm'  # 登录按钮
            },
            'trading': {
                'stock_code_input': 'stockCode',  # 股票代码输入框
                'stock_name_display': 'iptbdName',  # 股票名称显示
                'price_input': 'iptPrice',  # 价格输入框
                'quantity_input': 'iptCount',  # 数量输入框
                'confirm_button': 'btnConfirm',  # 确认按钮
                'reset_button': 'btnReset'  # 清空按钮
            },
            'urls': {
                'login': 'https://jywg.18.cn/Login',
                'buy': 'https://jywg.18.cn/Trade/Buy',
                'sell': 'https://jywg.18.cn/Trade/Sale'
            }
        }
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_label = tk.Label(self.master, text="基于源代码的网页交易测试", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # 元素信息显示
        info_frame = tk.LabelFrame(self.master, text="源代码元素信息", padx=10, pady=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_text = tk.Text(info_frame, height=8, width=100)
        info_text.pack(fill=tk.X)
        
        # 显示元素信息
        info_content = """登录页面元素：
• 资金账号输入框: id="txtZjzh" (第116行)
• 密码输入框: id="txtSec" (第125行)  
• 验证码输入框: id="txtValidCode" (第132行)
• 登录按钮: id="btnConfirm" (第145行)

交易页面元素：
• 股票代码输入框: id="stockCode" (第382行)
• 股票名称显示: id="iptbdName" (第388行)
• 价格输入框: id="iptPrice" (第409行)
• 数量输入框: id="iptCount" (第431行)
• 买入按钮: id="btnConfirm" (第455行)
• 清空按钮: id="btnReset" (第456行)"""
        
        info_text.insert(tk.END, info_content)
        info_text.config(state='disabled')
        
        # 测试按钮区域
        button_frame = tk.Frame(self.master)
        button_frame.pack(pady=20)
        
        self.connect_btn = tk.Button(button_frame, text="连接登录页面", 
                                    command=self.connect_login_page,
                                    bg='lightblue', width=15)
        self.connect_btn.pack(side=tk.LEFT, padx=5)
        
        self.test_login_elements_btn = tk.Button(button_frame, text="测试登录元素", 
                                               command=self.test_login_elements,
                                               bg='lightgreen', width=15)
        self.test_login_elements_btn.pack(side=tk.LEFT, padx=5)
        
        self.goto_trading_btn = tk.Button(button_frame, text="进入交易页面", 
                                         command=self.goto_trading_page,
                                         bg='lightyellow', width=15)
        self.goto_trading_btn.pack(side=tk.LEFT, padx=5)
        
        self.test_trading_elements_btn = tk.Button(button_frame, text="测试交易元素", 
                                                  command=self.test_trading_elements,
                                                  bg='lightcoral', width=15)
        self.test_trading_elements_btn.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = tk.LabelFrame(self.master, text="连接状态", padx=10, pady=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = tk.Label(status_frame, text="状态: 未连接", fg='red')
        self.status_label.pack(anchor='w')
        
        # 日志显示
        log_frame = tk.LabelFrame(self.master, text="测试日志", padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=100)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 初始化日志
        self.log_message("基于源代码的网页交易测试工具已启动")
        if SELENIUM_AVAILABLE:
            self.log_message("✓ Selenium环境正常")
        else:
            self.log_message("✗ Selenium环境异常")
    
    def log_message(self, message):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.master.update()
    
    def connect_login_page(self):
        """连接到登录页面"""
        if not SELENIUM_AVAILABLE:
            messagebox.showerror("错误", "Selenium不可用")
            return
            
        try:
            self.log_message("正在启动浏览器...")
            
            options = Options()
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            self.web_driver = webdriver.Edge(options=options)
            self.web_driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log_message("✓ 浏览器启动成功")
            
            # 访问登录页面
            login_url = self.elements['urls']['login']
            self.log_message(f"正在访问登录页面: {login_url}")
            self.web_driver.get(login_url)
            
            time.sleep(3)
            
            current_url = self.web_driver.current_url
            self.log_message(f"✓ 当前页面: {current_url}")
            
            self.is_connected = True
            self.status_label.config(text="状态: 已连接到登录页面", fg='green')
            
        except Exception as e:
            self.log_message(f"✗ 连接失败: {str(e)}")
            messagebox.showerror("连接失败", str(e))
    
    def test_login_elements(self):
        """测试登录页面元素"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到登录页面")
            return
            
        self.log_message("开始测试登录页面元素...")
        
        wait = WebDriverWait(self.web_driver, 10)
        
        # 测试各个登录元素
        login_elements = self.elements['login']
        
        for element_name, element_id in login_elements.items():
            try:
                element = wait.until(EC.presence_of_element_located((By.ID, element_id)))
                self.log_message(f"✓ 找到{element_name}: id='{element_id}'")
                
                # 显示元素的一些属性
                tag_name = element.tag_name
                element_type = element.get_attribute('type') or 'N/A'
                placeholder = element.get_attribute('placeholder') or 'N/A'
                self.log_message(f"  - 标签: {tag_name}, 类型: {element_type}, 占位符: {placeholder}")
                
            except Exception as e:
                self.log_message(f"✗ 未找到{element_name}: id='{element_id}' - {str(e)}")
        
        self.log_message("登录页面元素测试完成")
    
    def goto_trading_page(self):
        """进入交易页面"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接到登录页面")
            return
            
        self.log_message("正在导航到买入交易页面...")
        
        try:
            buy_url = self.elements['urls']['buy']
            self.web_driver.get(buy_url)
            
            time.sleep(3)
            
            current_url = self.web_driver.current_url
            self.log_message(f"✓ 当前页面: {current_url}")
            
            if "Trade" in current_url:
                self.log_message("✓ 成功进入交易页面")
                self.status_label.config(text="状态: 已进入交易页面", fg='blue')
            else:
                self.log_message("⚠ 可能需要先登录")
                
        except Exception as e:
            self.log_message(f"✗ 导航失败: {str(e)}")
    
    def test_trading_elements(self):
        """测试交易页面元素"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接")
            return
            
        self.log_message("开始测试交易页面元素...")
        
        wait = WebDriverWait(self.web_driver, 10)
        
        # 测试各个交易元素
        trading_elements = self.elements['trading']
        
        for element_name, element_id in trading_elements.items():
            try:
                element = wait.until(EC.presence_of_element_located((By.ID, element_id)))
                self.log_message(f"✓ 找到{element_name}: id='{element_id}'")
                
                # 显示元素的一些属性
                tag_name = element.tag_name
                element_type = element.get_attribute('type') or 'N/A'
                value = element.get_attribute('value') or 'N/A'
                disabled = element.get_attribute('disabled') or 'false'
                
                self.log_message(f"  - 标签: {tag_name}, 类型: {element_type}, 值: {value}, 禁用: {disabled}")
                
                # 如果是输入框，尝试测试输入
                if element_name.endswith('_input') and element.tag_name == 'input':
                    try:
                        element.clear()
                        test_value = "测试"
                        element.send_keys(test_value)
                        time.sleep(0.5)
                        current_value = element.get_attribute('value')
                        if test_value in current_value:
                            self.log_message(f"  ✓ 输入测试成功")
                        element.clear()  # 清空测试值
                    except Exception as input_e:
                        self.log_message(f"  ⚠ 输入测试失败: {str(input_e)}")
                
            except Exception as e:
                self.log_message(f"✗ 未找到{element_name}: id='{element_id}' - {str(e)}")
        
        self.log_message("交易页面元素测试完成")
    
    def __del__(self):
        """析构函数，清理资源"""
        try:
            if hasattr(self, 'web_driver') and self.web_driver:
                self.web_driver.quit()
        except:
            pass

def main():
    """主函数"""
    print("启动基于源代码的网页交易测试...")
    
    root = tk.Tk()
    app = SourceCodeBasedTradingTest(root)
    
    print("✓ 测试工具已启动")
    print("✓ 使用从源代码中提取的准确元素定位")
    
    root.mainloop()

if __name__ == "__main__":
    main()
