#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用者监控模块
用于监控用户的回测行为，收集回测数据和自定义公式，生成XLSX文件并上传
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import requests
from typing import Dict, Any, Optional
import hashlib
import time


class UserMonitor:
    """使用者监控类"""
    
    def __init__(self, config_dir: str = "user_config"):
        """
        初始化监控器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, "user_config.json")
        self.upload_url = "https://www.xiaoyouxices.cn/upload"
        
        # 确保配置目录存在
        os.makedirs(config_dir, exist_ok=True)
        
        # 加载用户配置
        self.user_config = self.load_user_config()
    
    def load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载用户配置失败: {e}")
                return {}
        return {}
    
    def save_user_config(self):
        """保存用户配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存用户配置失败: {e}")
    
    def set_phone_number(self, phone_number: str):
        """
        设置完整手机号

        Args:
            phone_number: 完整手机号
        """
        if phone_number and len(phone_number) >= 4:
            self.user_config['phone_number'] = phone_number  # 保存完整手机号
            phone_suffix = phone_number[-4:]
            self.user_config['phone_suffix'] = phone_suffix  # 同时保存后四位用于文件名
            self.user_config['last_login'] = datetime.now().isoformat()
            self.save_user_config()
            print(f"已保存完整手机号: {phone_number}")

    def get_phone_number(self) -> str:
        """获取完整手机号"""
        return self.user_config.get('phone_number', '')

    def get_phone_suffix(self) -> str:
        """获取手机号后四位"""
        return self.user_config.get('phone_suffix', '0000')

    # 保持向后兼容
    def set_phone_suffix(self, phone_number: str):
        """设置手机号后四位（向后兼容方法）"""
        self.set_phone_number(phone_number)
    
    def generate_filename(self) -> str:
        """
        根据文件生成时间和手机号后四位生成XLSX文件名
        
        Returns:
            生成的文件名
        """
        phone_suffix = self.get_phone_suffix()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"回测数据_{timestamp}_{phone_suffix}.xlsx"
        return filename
    
    def collect_backtest_data(self, backtest_results: Dict[str, Any], 
                            custom_strategy_code: str = "") -> Dict[str, Any]:
        """
        收集回测数据
        
        Args:
            backtest_results: 回测结果
            custom_strategy_code: 自定义策略代码
            
        Returns:
            整理后的数据
        """
        collected_data = {
            'timestamp': datetime.now().isoformat(),
            'phone_number': self.get_phone_number(),  # 使用完整手机号
            'phone_suffix': self.get_phone_suffix(),
            'backtest_results': {},
            'custom_strategy_code': custom_strategy_code,
            'performance_metrics': {}
        }
        
        # 提取关键回测指标
        if backtest_results:
            collected_data['backtest_results'] = {
                'initial_capital': backtest_results.get('initial_capital', 0),
                'final_value': backtest_results.get('final_value', 0),
                'total_return': backtest_results.get('total_return', 0),
                'annual_return': backtest_results.get('annual_return', 0),
                'volatility': backtest_results.get('volatility', 0),
                'sharpe_ratio': backtest_results.get('sharpe_ratio', 0),
                'max_drawdown': backtest_results.get('max_drawdown', 0),
                'total_trades': backtest_results.get('total_trades', 0),
                'win_rate': backtest_results.get('win_rate', 0)
            }
            
            # 提取资产曲线数据（如果存在）
            if 'equity_curve' in backtest_results:
                equity_curve = backtest_results['equity_curve']
                if isinstance(equity_curve, pd.DataFrame) and not equity_curve.empty:
                    collected_data['equity_curve'] = equity_curve.to_dict('records')
            
            # 提取交易记录（如果存在）
            if 'trades' in backtest_results:
                trades = backtest_results['trades']
                if isinstance(trades, pd.DataFrame) and not trades.empty:
                    collected_data['trades'] = trades.to_dict('records')
        
        return collected_data
    
    def create_xlsx_file(self, collected_data: Dict[str, Any], filename: str = None) -> str:
        """
        创建XLSX文件
        
        Args:
            collected_data: 收集的数据
            filename: 文件名（可选）
            
        Returns:
            生成的文件路径
        """
        if filename is None:
            filename = self.generate_filename()
        
        filepath = os.path.join(self.config_dir, filename)
        
        try:
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 基本信息表 - 在回测结果表中也包含自定义策略代码
                strategy_code = collected_data.get('custom_strategy_code', '')
                if not strategy_code:
                    strategy_code = '# 未使用自定义策略'

                basic_info = pd.DataFrame([{
                    '时间戳': collected_data['timestamp'],
                    '手机号': collected_data.get('phone_number', ''),  # 使用完整手机号
                    '手机号后四位': collected_data['phone_suffix'],
                    '初始资金': collected_data['backtest_results'].get('initial_capital', 0),
                    '最终资产': collected_data['backtest_results'].get('final_value', 0),
                    '总收益率': collected_data['backtest_results'].get('total_return', 0),
                    '年化收益率': collected_data['backtest_results'].get('annual_return', 0),
                    '波动率': collected_data['backtest_results'].get('volatility', 0),
                    '夏普比率': collected_data['backtest_results'].get('sharpe_ratio', 0),
                    '最大回撤': collected_data['backtest_results'].get('max_drawdown', 0),
                    '交易次数': collected_data['backtest_results'].get('total_trades', 0),
                    '胜率': collected_data['backtest_results'].get('win_rate', 0),
                    '自定义策略代码': strategy_code  # 在回测结果表中也包含策略代码
                }])
                basic_info.to_excel(writer, sheet_name='回测结果', index=False)

                # 自定义策略代码表 - 确保总是创建这个工作表
                strategy_code = collected_data.get('custom_strategy_code', '')
                if not strategy_code:
                    strategy_code = '# 未使用自定义策略'

                strategy_df = pd.DataFrame([{
                    '手机号': collected_data.get('phone_number', ''),
                    '时间戳': collected_data['timestamp'],
                    '自定义策略代码': strategy_code
                }])
                strategy_df.to_excel(writer, sheet_name='自定义公式', index=False)
                
                # 资产曲线表
                if 'equity_curve' in collected_data and collected_data['equity_curve']:
                    equity_df = pd.DataFrame(collected_data['equity_curve'])
                    equity_df.to_excel(writer, sheet_name='资产曲线', index=False)
                
                # 交易记录表
                if 'trades' in collected_data and collected_data['trades']:
                    trades_df = pd.DataFrame(collected_data['trades'])
                    trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            
            print(f"XLSX文件已生成: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"生成XLSX文件失败: {e}")
            return ""
    
    def upload_file(self, filepath: str, max_retries: int = 3) -> bool:
        """
        同步上传文件到指定网址（带重试机制）

        Args:
            filepath: 文件路径
            max_retries: 最大重试次数

        Returns:
            上传是否成功
        """
        if not os.path.exists(filepath):
            print(f"文件不存在: {filepath}")
            return False

        filename = os.path.basename(filepath)
        file_size = os.path.getsize(filepath)

        print(f"开始同步上传文件: {filename} (大小: {file_size} 字节)")

        for attempt in range(max_retries):
            try:
                print(f"尝试上传 ({attempt + 1}/{max_retries})...")

                with open(filepath, 'rb') as f:
                    files = {
                        'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                    }

                    # 设置请求头
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': '*/*',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive'
                    }

                    # 同步上传请求
                    print("正在发送上传请求...")
                    response = requests.post(
                        self.upload_url,
                        files=files,
                        headers=headers,
                        timeout=60,  # 增加超时时间
                        stream=False  # 确保同步等待完整响应
                    )

                    print(f"收到响应，状态码: {response.status_code}")

                    if response.status_code == 200:
                        print(f"✅ 文件上传成功! 响应: {response.text[:200]}")
                        return True
                    elif response.status_code == 413:
                        print(f"❌ 文件太大，无法上传 (状态码: {response.status_code})")
                        return False
                    else:
                        print(f"⚠️ 上传失败，状态码: {response.status_code}")
                        print(f"响应内容: {response.text[:500]}")

                        if attempt < max_retries - 1:
                            wait_time = (attempt + 1) * 2  # 递增等待时间
                            print(f"等待 {wait_time} 秒后重试...")
                            time.sleep(wait_time)

            except requests.exceptions.Timeout as e:
                print(f"⚠️ 上传超时: {e}")
                if attempt < max_retries - 1:
                    print("等待 5 秒后重试...")
                    time.sleep(5)

            except requests.exceptions.ConnectionError as e:
                print(f"⚠️ 连接错误: {e}")
                if attempt < max_retries - 1:
                    print("等待 3 秒后重试...")
                    time.sleep(3)

            except requests.exceptions.RequestException as e:
                print(f"⚠️ 网络请求错误: {e}")
                if attempt < max_retries - 1:
                    print("等待 2 秒后重试...")
                    time.sleep(2)

            except Exception as e:
                print(f"❌ 上传过程中发生未知错误: {e}")
                if attempt < max_retries - 1:
                    print("等待 1 秒后重试...")
                    time.sleep(1)

        print(f"❌ 文件上传失败，已尝试 {max_retries} 次")
        return False
    
    def process_backtest_completion(self, backtest_results: Dict[str, Any],
                                  custom_strategy_code: str = "") -> bool:
        """
        同步处理回测完成事件

        Args:
            backtest_results: 回测结果
            custom_strategy_code: 自定义策略代码

        Returns:
            处理是否成功
        """
        try:
            print("=" * 60)
            print("开始同步处理回测完成事件...")
            print("=" * 60)

            # 步骤1: 收集数据
            print("📊 步骤1: 收集回测数据...")
            collected_data = self.collect_backtest_data(backtest_results, custom_strategy_code)
            phone_number = collected_data.get('phone_number', '未知')
            print(f"   ✅ 数据收集完成 (用户: {phone_number})")

            # 步骤2: 生成XLSX文件
            print("📄 步骤2: 生成XLSX文件...")
            filepath = self.create_xlsx_file(collected_data)
            if not filepath:
                print("   ❌ XLSX文件生成失败")
                return False

            file_size = os.path.getsize(filepath)
            print(f"   ✅ XLSX文件生成成功 (大小: {file_size} 字节)")

            # 步骤3: 同步上传文件
            print("🌐 步骤3: 同步上传文件...")
            print(f"   目标网址: {self.upload_url}")

            upload_success = self.upload_file(filepath, max_retries=3)

            # 步骤4: 记录处理结果
            print("💾 步骤4: 保存处理结果...")
            self.user_config['last_backtest'] = {
                'timestamp': collected_data['timestamp'],
                'filename': os.path.basename(filepath),
                'upload_success': upload_success,
                'file_size': file_size,
                'phone_number': phone_number
            }
            self.save_user_config()
            print("   ✅ 处理结果已保存")

            print("=" * 60)
            if upload_success:
                print("🎉 回测监控处理完成 - 所有步骤成功!")
            else:
                print("⚠️ 回测监控处理完成 - 上传失败")
            print("=" * 60)

            return upload_success

        except Exception as e:
            print(f"❌ 处理回测完成事件失败: {e}")
            print("=" * 60)
            return False


# 全局监控器实例
_monitor_instance = None

def get_monitor() -> UserMonitor:
    """获取全局监控器实例"""
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = UserMonitor()
    return _monitor_instance

def set_user_phone(phone_number: str):
    """设置用户手机号"""
    monitor = get_monitor()
    monitor.set_phone_number(phone_number)  # 使用新的方法保存完整手机号

def monitor_backtest_completion(backtest_results: Dict[str, Any], 
                              custom_strategy_code: str = "") -> bool:
    """监控回测完成事件"""
    monitor = get_monitor()
    return monitor.process_backtest_completion(backtest_results, custom_strategy_code)
