#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多股票监控系统配置文件
包含所有可配置的参数和默认值
"""

import json
import os
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class MultiStockConfig:
    """多股票监控配置管理器"""
    
    def __init__(self, config_file: str = "multi_stock_config.json"):
        self.config_file = config_file
        self.config = self._load_default_config()
        self.load_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            # 监控设置
            "monitor": {
                "max_stocks": 200,                    # 最大监控股票数
                "default_update_interval": 30,       # 默认更新间隔（秒）
                "min_update_interval": 10,           # 最小更新间隔（秒）
                "max_update_interval": 300,          # 最大更新间隔（秒）
                "batch_size": 30,                    # 批量获取数据的批次大小
                "max_history_points": 100,           # 每只股票最大历史数据点数
                "cleanup_interval": 300,             # 数据清理间隔（秒）
                "performance_check_interval": 10     # 性能检查间隔（循环次数）
            },
            
            # API设置
            "api": {
                "max_calls_per_minute": 60,          # 每分钟最大API调用次数
                "request_timeout": 10,               # 请求超时时间（秒）
                "retry_count": 3,                    # 失败重试次数
                "retry_delay": 1,                    # 重试延迟（秒）
                "batch_delay": 0.2,                  # 批次间延迟（秒）
                "slow_response_threshold": 1.0       # 慢响应阈值（秒）
            },
            
            # 策略设置
            "strategy": {
                "default_strategy": "MACD",          # 默认策略类型
                "macd_params": {
                    "fast_period": 12,
                    "slow_period": 26,
                    "signal_period": 9
                },
                "kdj_params": {
                    "k_period": 9,
                    "oversold": 20,
                    "overbought": 80
                },
                "custom_strategy_code": ""           # 自定义策略代码
            },
            
            # 风险控制设置
            "risk_control": {
                "max_position_per_stock": 10000,    # 单股票最大持仓数量
                "max_total_positions": 50,          # 最大持仓股票数量
                "max_daily_trades": 100,            # 每日最大交易次数
                "max_trade_amount": 100000,         # 单笔最大交易金额
                "min_trade_interval": 60,           # 同一股票最小交易间隔（秒）
                "default_trade_amount": 10000,      # 默认单股票交易金额
                "position_size_method": "fixed",    # 仓位计算方法：fixed/percentage
                "max_portfolio_risk": 0.02          # 最大组合风险比例
            },
            
            # 交易调度设置
            "trading": {
                "max_concurrent_trades": 3,         # 最大并发交易数
                "trade_timeout": 30,                # 交易超时时间（秒）
                "signal_priority_buy": 1,           # 买入信号优先级
                "signal_priority_sell": 1,          # 卖出信号优先级
                "auto_trading_enabled": False,      # 默认自动交易状态
                "trade_confirmation": True          # 是否需要交易确认
            },
            
            # 界面设置
            "ui": {
                "refresh_interval": 1000,           # 界面刷新间隔（毫秒）
                "max_log_lines": 1000,              # 最大日志行数
                "max_signal_display": 100,          # 最大信号显示数量
                "table_row_height": 25,             # 表格行高
                "auto_scroll_logs": True,           # 自动滚动日志
                "show_performance_metrics": True    # 显示性能指标
            },
            
            # 数据源设置
            "data_source": {
                "primary_source": "tushare",        # 主要数据源
                "backup_source": "akshare",         # 备用数据源
                "use_cache": True,                  # 是否使用缓存
                "cache_expire_time": 60,            # 缓存过期时间（秒）
                "realtime_data_source": "sina"     # 实时数据源
            },
            
            # 日志设置
            "logging": {
                "level": "INFO",                    # 日志级别
                "file_enabled": True,               # 是否写入文件
                "file_path": "logs/multi_stock.log", # 日志文件路径
                "max_file_size": 10,                # 最大文件大小（MB）
                "backup_count": 5,                  # 备份文件数量
                "console_enabled": True             # 是否输出到控制台
            },
            
            # 性能设置
            "performance": {
                "thread_pool_size": 10,             # 线程池大小
                "memory_limit_mb": 500,             # 内存使用限制（MB）
                "cpu_usage_threshold": 80,          # CPU使用率阈值（%）
                "auto_optimization": True,          # 自动性能优化
                "gc_interval": 100                  # 垃圾回收间隔（循环次数）
            },
            
            # 股票池设置
            "stock_pools": {
                "沪深300": "hs300",
                "中证1000": "zz1000", 
                "上证50": "sz50",
                "创业板": "cyb",
                "科创板": "kcb",
                "自定义": "custom"
            },
            
            # 通知设置
            "notification": {
                "signal_notification": True,        # 信号通知
                "trade_notification": True,         # 交易通知
                "error_notification": True,         # 错误通知
                "performance_notification": False,  # 性能通知
                "notification_method": "popup"      # 通知方式：popup/email/wechat
            }
        }
    
    def load_config(self):
        """从文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                
                # 递归更新配置
                self._update_config(self.config, file_config)
                logger.info(f"配置已从 {self.config_file} 加载")
                
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
        else:
            logger.info("配置文件不存在，使用默认配置")
            self.save_config()  # 保存默认配置
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file) if os.path.dirname(self.config_file) else '.', exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置已保存到 {self.config_file}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def _update_config(self, base_config: Dict, update_config: Dict):
        """递归更新配置"""
        for key, value in update_config.items():
            if key in base_config:
                if isinstance(base_config[key], dict) and isinstance(value, dict):
                    self._update_config(base_config[key], value)
                else:
                    base_config[key] = value
            else:
                base_config[key] = value
    
    def get(self, section: str, key: str = None, default=None):
        """获取配置值"""
        try:
            if key is None:
                return self.config.get(section, default)
            else:
                return self.config.get(section, {}).get(key, default)
        except:
            return default
    
    def set(self, section: str, key: str = None, value=None):
        """设置配置值"""
        try:
            if key is None:
                self.config[section] = value
            else:
                if section not in self.config:
                    self.config[section] = {}
                self.config[section][key] = value
            
            logger.debug(f"配置已更新: {section}.{key} = {value}")
            
        except Exception as e:
            logger.error(f"设置配置失败: {e}")
    
    def get_monitor_config(self) -> Dict:
        """获取监控配置"""
        return self.config.get("monitor", {})
    
    def get_api_config(self) -> Dict:
        """获取API配置"""
        return self.config.get("api", {})
    
    def get_strategy_config(self) -> Dict:
        """获取策略配置"""
        return self.config.get("strategy", {})
    
    def get_risk_config(self) -> Dict:
        """获取风险控制配置"""
        return self.config.get("risk_control", {})
    
    def get_trading_config(self) -> Dict:
        """获取交易配置"""
        return self.config.get("trading", {})
    
    def get_ui_config(self) -> Dict:
        """获取界面配置"""
        return self.config.get("ui", {})
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证监控配置
            monitor_config = self.get_monitor_config()
            if monitor_config.get("max_stocks", 0) <= 0:
                logger.error("max_stocks 必须大于0")
                return False
            
            if monitor_config.get("default_update_interval", 0) < monitor_config.get("min_update_interval", 10):
                logger.error("default_update_interval 不能小于 min_update_interval")
                return False
            
            # 验证风险控制配置
            risk_config = self.get_risk_config()
            if risk_config.get("max_position_per_stock", 0) <= 0:
                logger.error("max_position_per_stock 必须大于0")
                return False
            
            if risk_config.get("max_trade_amount", 0) <= 0:
                logger.error("max_trade_amount 必须大于0")
                return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self._load_default_config()
        self.save_config()
        logger.info("配置已重置为默认值")
    
    def export_config(self, export_file: str):
        """导出配置到指定文件"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置已导出到 {export_file}")
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_file: str):
        """从指定文件导入配置"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            self._update_config(self.config, imported_config)
            self.save_config()
            logger.info(f"配置已从 {import_file} 导入")
            return True
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False


# 全局配置实例
config = MultiStockConfig()


def get_config():
    """获取全局配置实例"""
    return config


if __name__ == "__main__":
    # 测试配置管理器
    config = MultiStockConfig()
    
    # 验证配置
    if config.validate_config():
        print("配置验证通过")
    
    # 测试获取配置
    print(f"最大监控股票数: {config.get('monitor', 'max_stocks')}")
    print(f"默认更新间隔: {config.get('monitor', 'default_update_interval')}")
    
    # 测试设置配置
    config.set('monitor', 'max_stocks', 150)
    print(f"修改后最大监控股票数: {config.get('monitor', 'max_stocks')}")
    
    # 保存配置
    config.save_config()
    print("配置已保存")
