#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用者监控功能演示
展示完整的监控流程：从登录到回测完成再到数据上传
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import numpy as np
from datetime import datetime
import threading
import time

from 使用者监控 import UserMonitor, set_user_phone, monitor_backtest_completion

class MonitorDemo:
    def __init__(self, master):
        self.master = master
        master.title("使用者监控功能演示")
        master.geometry("800x600")
        
        # 创建界面
        self.create_widgets()
        
        # 初始化监控器
        self.monitor = UserMonitor()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_label = tk.Label(self.master, text="使用者监控功能演示", 
                              font=("Arial", 16, "bold"), fg="blue")
        title_label.pack(pady=10)
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.master)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 第一步：模拟登录
        self.create_login_tab()
        
        # 第二步：模拟回测
        self.create_backtest_tab()
        
        # 第三步：查看结果
        self.create_result_tab()
        
    def create_login_tab(self):
        """创建登录演示标签页"""
        login_frame = ttk.Frame(self.notebook)
        self.notebook.add(login_frame, text="1. 模拟登录")
        
        # 说明文字
        info_label = tk.Label(login_frame, 
                             text="这里模拟用户登录过程，登录成功后会保存手机号后四位",
                             font=("Arial", 12), wraplength=600)
        info_label.pack(pady=20)
        
        # 手机号输入
        phone_frame = tk.Frame(login_frame)
        phone_frame.pack(pady=20)
        
        tk.Label(phone_frame, text="手机号:", font=("Arial", 12)).pack(side=tk.LEFT)
        self.phone_var = tk.StringVar(value="13812345678")
        phone_entry = tk.Entry(phone_frame, textvariable=self.phone_var, 
                              font=("Arial", 12), width=15)
        phone_entry.pack(side=tk.LEFT, padx=10)
        
        # 登录按钮
        login_btn = tk.Button(login_frame, text="模拟登录", 
                             command=self.simulate_login,
                             bg='lightgreen', font=("Arial", 12))
        login_btn.pack(pady=10)
        
        # 状态显示
        self.login_status = tk.Label(login_frame, text="", 
                                    font=("Arial", 11), fg="green")
        self.login_status.pack(pady=10)
        
    def create_backtest_tab(self):
        """创建回测演示标签页"""
        backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(backtest_frame, text="2. 模拟回测")
        
        # 说明文字
        info_label = tk.Label(backtest_frame, 
                             text="这里模拟回测过程，回测完成后会自动生成XLSX文件并上传",
                             font=("Arial", 12), wraplength=600)
        info_label.pack(pady=20)
        
        # 策略选择
        strategy_frame = tk.Frame(backtest_frame)
        strategy_frame.pack(pady=20)
        
        tk.Label(strategy_frame, text="策略类型:", font=("Arial", 12)).pack(side=tk.LEFT)
        self.strategy_var = tk.StringVar(value="移动平均策略")
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.strategy_var,
                                     values=["移动平均策略", "MACD策略", "KDJ策略", "自定义策略"],
                                     state="readonly", font=("Arial", 12))
        strategy_combo.pack(side=tk.LEFT, padx=10)
        
        # 回测按钮
        backtest_btn = tk.Button(backtest_frame, text="开始回测", 
                                command=self.simulate_backtest,
                                bg='lightblue', font=("Arial", 12))
        backtest_btn.pack(pady=10)
        
        # 进度条
        self.progress = ttk.Progressbar(backtest_frame, mode='indeterminate')
        self.progress.pack(pady=10, fill=tk.X, padx=50)
        
        # 状态显示
        self.backtest_status = tk.Label(backtest_frame, text="", 
                                       font=("Arial", 11), fg="blue")
        self.backtest_status.pack(pady=10)
        
    def create_result_tab(self):
        """创建结果查看标签页"""
        result_frame = ttk.Frame(self.notebook)
        self.notebook.add(result_frame, text="3. 查看结果")
        
        # 说明文字
        info_label = tk.Label(result_frame, 
                             text="查看监控系统收集的数据和上传结果",
                             font=("Arial", 12))
        info_label.pack(pady=20)
        
        # 结果显示区域
        self.result_text = tk.Text(result_frame, height=20, width=80, 
                                  font=("Consolas", 10))
        scrollbar = tk.Scrollbar(result_frame, orient=tk.VERTICAL, 
                                command=self.result_text.yview)
        self.result_text.config(yscrollcommand=scrollbar.set)
        
        text_frame = tk.Frame(result_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 刷新按钮
        refresh_btn = tk.Button(result_frame, text="刷新结果", 
                               command=self.refresh_results,
                               bg='lightyellow', font=("Arial", 12))
        refresh_btn.pack(pady=10)
        
    def simulate_login(self):
        """模拟登录过程"""
        phone = self.phone_var.get().strip()
        if not phone:
            messagebox.showerror("错误", "请输入手机号")
            return
            
        if len(phone) < 4:
            messagebox.showerror("错误", "手机号长度不足")
            return
        
        # 模拟登录过程
        self.login_status.config(text="正在登录...", fg="orange")
        self.master.update()
        
        def login_process():
            time.sleep(2)  # 模拟登录延迟
            
            # 保存手机号到监控系统
            set_user_phone(phone)
            
            # 更新状态
            self.master.after(0, lambda: self.login_status.config(
                text=f"✅ 登录成功！手机号后四位已保存: {phone[-4:]}", fg="green"))
            
            # 自动切换到下一个标签页
            self.master.after(1000, lambda: self.notebook.select(1))
        
        threading.Thread(target=login_process, daemon=True).start()
        
    def simulate_backtest(self):
        """模拟回测过程"""
        strategy = self.strategy_var.get()
        
        # 开始进度条
        self.progress.start()
        self.backtest_status.config(text="正在运行回测...", fg="blue")
        
        def backtest_process():
            # 模拟回测过程
            time.sleep(3)
            
            # 创建模拟回测结果
            results = self.create_mock_results(strategy)
            custom_code = self.create_mock_strategy_code(strategy)
            
            # 更新状态
            self.master.after(0, lambda: self.backtest_status.config(
                text="正在生成报告和上传数据...", fg="orange"))
            
            # 调用监控系统
            success = monitor_backtest_completion(results, custom_code)
            
            # 停止进度条
            self.master.after(0, self.progress.stop)
            
            if success:
                self.master.after(0, lambda: self.backtest_status.config(
                    text="✅ 回测完成，数据已成功上传！", fg="green"))
            else:
                self.master.after(0, lambda: self.backtest_status.config(
                    text="❌ 回测完成，但数据上传失败", fg="red"))
            
            # 自动切换到结果页面
            self.master.after(1000, lambda: self.notebook.select(2))
            self.master.after(1500, self.refresh_results)
        
        threading.Thread(target=backtest_process, daemon=True).start()
        
    def create_mock_results(self, strategy):
        """创建模拟回测结果"""
        # 根据策略类型生成不同的收益率
        strategy_returns = {
            "移动平均策略": 0.25,
            "MACD策略": 0.35,
            "KDJ策略": 0.18,
            "自定义策略": 0.45
        }
        
        base_return = strategy_returns.get(strategy, 0.20)
        
        return {
            'initial_capital': 100000,
            'final_value': 100000 * (1 + base_return),
            'total_return': base_return,
            'annual_return': base_return * 1.2,
            'volatility': 0.15,
            'sharpe_ratio': 1.5,
            'max_drawdown': -0.08,
            'total_trades': 25,
            'win_rate': 0.68
        }
    
    def create_mock_strategy_code(self, strategy):
        """创建模拟策略代码"""
        codes = {
            "移动平均策略": "data['MA5'] = data['close'].rolling(5).mean()\ndata['signal'] = (data['close'] > data['MA5']).astype(int)",
            "MACD策略": "data['MACD'] = MACD(data['close'])\ndata['signal'] = (data['MACD'] > 0).astype(int)",
            "KDJ策略": "data['K'], data['D'], data['J'] = KDJ(data)\ndata['signal'] = (data['K'] > data['D']).astype(int)",
            "自定义策略": "# 自定义复合策略\ndata['RSI'] = RSI(data['close'])\ndata['signal'] = ((data['RSI'] < 30) | (data['RSI'] > 70)).astype(int)"
        }
        return codes.get(strategy, "# 默认策略")
    
    def refresh_results(self):
        """刷新结果显示"""
        self.result_text.delete(1.0, tk.END)
        
        # 显示监控系统状态
        try:
            phone_suffix = self.monitor.get_phone_suffix()
            config = self.monitor.user_config
            
            self.result_text.insert(tk.END, "=" * 60 + "\n")
            self.result_text.insert(tk.END, "使用者监控系统状态\n")
            self.result_text.insert(tk.END, "=" * 60 + "\n\n")
            
            self.result_text.insert(tk.END, f"手机号后四位: {phone_suffix}\n")
            
            if 'last_login' in config:
                self.result_text.insert(tk.END, f"最后登录时间: {config['last_login']}\n")
            
            if 'last_backtest' in config:
                backtest_info = config['last_backtest']
                self.result_text.insert(tk.END, f"\n最近回测信息:\n")
                self.result_text.insert(tk.END, f"  时间: {backtest_info['timestamp']}\n")
                self.result_text.insert(tk.END, f"  文件名: {backtest_info['filename']}\n")
                self.result_text.insert(tk.END, f"  上传状态: {'✅ 成功' if backtest_info['upload_success'] else '❌ 失败'}\n")
            
            # 显示生成的文件
            import os
            config_dir = self.monitor.config_dir
            if os.path.exists(config_dir):
                files = [f for f in os.listdir(config_dir) if f.endswith('.xlsx')]
                if files:
                    self.result_text.insert(tk.END, f"\n生成的XLSX文件:\n")
                    for file in files:
                        filepath = os.path.join(config_dir, file)
                        file_size = os.path.getsize(filepath)
                        self.result_text.insert(tk.END, f"  📄 {file} ({file_size} 字节)\n")
            
            self.result_text.insert(tk.END, "\n" + "=" * 60 + "\n")
            self.result_text.insert(tk.END, "监控功能运行正常！\n")
            self.result_text.insert(tk.END, "每次回测完成后都会自动生成XLSX文件并上传到指定网址。\n")
            
        except Exception as e:
            self.result_text.insert(tk.END, f"获取监控状态时出错: {e}\n")

def main():
    root = tk.Tk()
    app = MonitorDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main()
