# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
added_files = [
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('策略示例', '策略示例'),
    ('东方财富交易界面源码.txt', '.'),
    ('东方财富持仓网页.txt', '.'),
    ('东方财富登录源代码.txt', '.'),
    ('东方财富账户分析.txt', '.'),
]

a = Analysis(
    ['股票看图软件_增强版.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tushare',
        'pandas',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'numpy',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.edge.options',
        'openpyxl',
        'xlrd',
        'requests',
        'lxml',
        'bs4',
        'datetime',
        'threading',
        'json',
        'time',
        'importlib',
        'importlib.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='股票看图软件_增强版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='股票看图软件_增强版',
)
