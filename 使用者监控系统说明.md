# 使用者监控系统说明

## 概述

使用者监控系统是一个自动化的数据收集和上传系统，用于监控用户的回测行为，收集回测数据和自定义公式，生成XLSX文件并自动上传到指定网址。

## 功能特性

### 1. 用户身份识别
- 在用户登录成功时自动保存手机号后四位
- 使用手机号后四位作为用户标识
- 支持登录和注册两种方式

### 2. 回测数据收集
- 自动收集回测完成后的所有关键指标
- 包括收益率、夏普比率、最大回撤等
- 保存资产曲线和交易记录详细数据
- 收集用户的自定义策略代码

### 3. XLSX文件生成
- 根据文件生成时间和手机号后四位生成唯一文件名
- 格式：`回测数据_YYYYMMDD_HHMMSS_手机号后四位.xlsx`
- 包含多个工作表：
  - **回测结果**：基本回测指标
  - **自定义公式**：用户的策略代码
  - **资产曲线**：详细的资产变化数据
  - **交易记录**：所有买卖交易详情

### 4. 自动上传功能
- 自动上传到 https://www.xiaoyouxices.cn/ 网址
- 支持网络重试机制
- 上传状态记录和反馈

## 系统架构

```
登录注册.py (入口程序)
    ↓ 登录成功
    ↓ 保存手机号后四位
    ↓
股票看图软件_增强版.py (主程序)
    ↓ 回测完成
    ↓ 调用监控系统
    ↓
使用者监控.py (监控模块)
    ↓ 收集数据
    ↓ 生成XLSX
    ↓ 上传文件
    ↓
https://www.xiaoyouxices.cn/ (上传目标)
```

## 文件结构

```
项目根目录/
├── 登录注册.py                    # 程序入口，处理用户登录
├── 股票看图软件_增强版.py          # 主程序，集成监控功能
├── 使用者监控.py                  # 监控模块核心代码
├── 测试监控功能.py                # 功能测试脚本
├── 监控功能演示.py                # 演示程序
├── user_config/                   # 用户配置和数据目录
│   ├── user_config.json          # 用户配置文件
│   └── 回测数据_*.xlsx           # 生成的XLSX文件
└── 使用者监控系统说明.md          # 本说明文件
```

## 使用流程

### 1. 用户登录
1. 运行 `登录注册.py`
2. 输入手机号和验证码
3. 登录成功后，系统自动保存手机号后四位
4. 自动启动股票看图软件

### 2. 进行回测
1. 在股票看图软件中选择股票和策略
2. 设置回测参数
3. 运行回测
4. 回测完成后，监控系统自动激活

### 3. 自动监控
1. 系统自动收集回测结果数据
2. 收集用户的自定义策略代码
3. 生成包含完整数据的XLSX文件
4. 自动上传到指定网址
5. 在界面显示上传状态

## 配置文件说明

### user_config.json
```json
{
  "phone_suffix": "5678",                    // 手机号后四位
  "last_login": "2025-07-27T22:11:21.581821", // 最后登录时间
  "last_backtest": {                         // 最近回测信息
    "timestamp": "2025-07-27T22:11:21.820249",
    "filename": "回测数据_20250727_221121_5678.xlsx",
    "upload_success": true
  }
}
```

## XLSX文件内容

### 回测结果工作表
| 字段 | 说明 |
|------|------|
| 时间戳 | 回测完成时间 |
| 手机号后四位 | 用户标识 |
| 初始资金 | 回测起始资金 |
| 最终资产 | 回测结束时总资产 |
| 总收益率 | 整个回测期间的收益率 |
| 年化收益率 | 年化后的收益率 |
| 波动率 | 策略的风险指标 |
| 夏普比率 | 风险调整后收益 |
| 最大回撤 | 最大亏损幅度 |
| 交易次数 | 总交易笔数 |
| 胜率 | 盈利交易占比 |

### 自定义公式工作表
包含用户编写的完整策略代码，便于后续分析和复现。

### 资产曲线工作表
记录每日的资产变化情况，包括：
- 日期
- 总资产价值
- 现金余额
- 持仓价值
- 日收益率

### 交易记录工作表
详细记录每笔交易：
- 股票代码
- 交易类型（买入/卖出）
- 交易数量
- 交易价格
- 交易日期
- 手续费
- 交易金额

## 测试和演示

### 运行功能测试
```bash
python 测试监控功能.py
```

### 运行演示程序
```bash
python 监控功能演示.py
```

## 技术实现

### 核心类：UserMonitor
- `set_phone_suffix()`: 设置用户手机号后四位
- `collect_backtest_data()`: 收集回测数据
- `create_xlsx_file()`: 生成XLSX文件
- `upload_file()`: 上传文件到指定网址
- `process_backtest_completion()`: 处理回测完成事件

### 集成点
1. **登录注册.py**: 在 `launch_stock_viewer()` 方法中添加手机号保存
2. **股票看图软件_增强版.py**: 在 `display_backtest_results()` 和 `display_multi_stock_results()` 方法中添加监控调用

### 依赖库
- pandas: 数据处理
- openpyxl: Excel文件生成
- requests: HTTP上传
- json: 配置文件处理

## 注意事项

1. **网络连接**: 上传功能需要稳定的网络连接
2. **文件权限**: 确保程序有权限在user_config目录创建文件
3. **磁盘空间**: XLSX文件会占用一定磁盘空间，建议定期清理
4. **隐私保护**: 只保存手机号后四位，不保存完整手机号

## 故障排除

### 上传失败
1. 检查网络连接
2. 确认目标网址可访问
3. 查看错误日志

### 文件生成失败
1. 检查磁盘空间
2. 确认目录权限
3. 检查pandas和openpyxl库是否正确安装

### 监控不工作
1. 确认监控模块正确导入
2. 检查回测是否正常完成
3. 查看控制台错误信息

## 更新日志

### v1.0.0 (2025-07-27)
- 初始版本发布
- 实现基本监控功能
- 支持XLSX文件生成和上传
- 集成到登录和回测系统
