#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录界面状态标签功能
"""

import tkinter as tk
from tkinter import ttk
import time

def test_status_labels():
    """测试状态标签功能"""
    
    root = tk.Tk()
    root.title("测试状态标签")
    root.geometry("500x400")
    
    # 居中显示
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (500 // 2)
    y = (root.winfo_screenheight() // 2) - (400 // 2)
    root.geometry(f"500x400+{x}+{y}")
    
    # 创建主框架
    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    tk.Label(main_frame, text="登录界面状态标签测试", font=("Arial", 16, "bold")).pack(pady=10)
    
    # 模拟登录表单
    form_frame = tk.Frame(main_frame)
    form_frame.pack(fill=tk.X, pady=10)
    
    tk.Label(form_frame, text="手机号:").pack(anchor='w')
    phone_entry = tk.Entry(form_frame, width=30)
    phone_entry.pack(fill=tk.X, pady=2)
    
    tk.Label(form_frame, text="验证码:").pack(anchor='w')
    captcha_entry = tk.Entry(form_frame, width=30)
    captcha_entry.pack(fill=tk.X, pady=2)
    
    # 登录按钮
    login_btn = tk.Button(form_frame, text="登录", bg='lightgreen')
    login_btn.pack(pady=10)
    
    # 状态标签（这是我们要测试的功能）
    status_label = tk.Label(form_frame, text="", 
                           font=("Arial", 11, "bold"), 
                           fg="green", wraplength=400)
    status_label.pack(pady=10)
    
    # 测试按钮
    test_frame = tk.Frame(main_frame)
    test_frame.pack(fill=tk.X, pady=20)
    
    tk.Label(test_frame, text="测试不同的状态提示:", font=("Arial", 12, "bold")).pack(pady=5)
    
    def show_success():
        status_label.config(text="✓ 登录成功！正在启动股票看图软件...", fg="green")
    
    def show_register_success():
        status_label.config(text="✓ 注册成功！正在启动股票看图软件...", fg="green")
    
    def show_error():
        status_label.config(text="✗ 登录失败，请检查手机号和验证码", fg="red")
    
    def show_loading():
        status_label.config(text="⏳ 正在验证登录信息...", fg="blue")
    
    def clear_status():
        status_label.config(text="")
    
    # 测试按钮
    btn_frame = tk.Frame(test_frame)
    btn_frame.pack(fill=tk.X)
    
    tk.Button(btn_frame, text="登录成功", command=show_success, 
             bg='lightgreen', width=12).pack(side=tk.LEFT, padx=2)
    
    tk.Button(btn_frame, text="注册成功", command=show_register_success, 
             bg='lightblue', width=12).pack(side=tk.LEFT, padx=2)
    
    tk.Button(btn_frame, text="显示错误", command=show_error, 
             bg='lightcoral', width=12).pack(side=tk.LEFT, padx=2)
    
    tk.Button(btn_frame, text="显示加载", command=show_loading, 
             bg='lightyellow', width=12).pack(side=tk.LEFT, padx=2)
    
    tk.Button(btn_frame, text="清除状态", command=clear_status, 
             bg='lightgray', width=12).pack(side=tk.LEFT, padx=2)
    
    # 说明文字
    info_frame = tk.Frame(main_frame)
    info_frame.pack(fill=tk.X, pady=10)
    
    info_text = """
使用说明:
1. 点击上方按钮测试不同的状态提示效果
2. "登录成功"和"注册成功"会显示绿色的成功提示
3. "显示错误"会显示红色的错误提示
4. "显示加载"会显示蓝色的加载提示
5. "清除状态"会清空状态文字

这就是在实际登录界面中会看到的效果！
    """
    
    tk.Label(info_frame, text=info_text, font=("Arial", 9), 
            justify=tk.LEFT, fg="gray").pack(anchor='w')
    
    # 退出按钮
    tk.Button(main_frame, text="退出测试", command=root.quit, 
             bg='orange', font=("Arial", 10)).pack(pady=10)
    
    print("状态标签测试窗口已打开")
    print("点击按钮测试不同的状态提示效果")
    
    root.mainloop()

if __name__ == "__main__":
    test_status_labels()
