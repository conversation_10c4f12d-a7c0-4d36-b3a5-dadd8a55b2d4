#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录注册程序打包脚本
使用PyInstaller将登录注册.py打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def check_dependencies():
    """检查必要的依赖库"""
    required_packages = [
        'tkinter',
        'selenium', 
        'webdriver-manager',
        'Pillow',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'selenium':
                import selenium
            elif package == 'webdriver-manager':
                import webdriver_manager
            elif package == 'Pillow':
                import PIL
            elif package == 'requests':
                import requests
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖库"""
    if not packages:
        return True
        
    print(f"正在安装缺失的依赖库: {', '.join(packages)}")
    try:
        # 特殊处理Pillow
        install_packages = []
        for pkg in packages:
            if pkg == 'Pillow':
                install_packages.append('Pillow')
            elif pkg == 'tkinter':
                print("注意: tkinter是Python内置库，如果缺失请重新安装Python")
                continue
            else:
                install_packages.append(pkg)
        
        if install_packages:
            subprocess.check_call([sys.executable, "-m", "pip", "install"] + install_packages)
        print("✓ 依赖库安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖库安装失败")
        return False

def create_login_spec_file():
    """创建登录程序的PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
added_files = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('多股票监控管理器.py', '.'),
    ('交易调度器.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
]

a = Analysis(
    ['登录注册.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.scrolledtext',
        
        # 网页自动化
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service', 
        'selenium.webdriver.chrome.options',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        
        # 图像处理
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # HTTP和数据处理
        'requests',
        'base64',
        'io',
        'os',
        'threading',
        'time',
        'subprocess',
        'sys',
        'json',
        
        # 数据分析库（股票软件需要）
        'pandas',
        'numpy',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'tushare',
        'seaborn',
        'openpyxl',
        'xlrd',
        'lxml',
        'bs4',
        
        # 日期时间
        'datetime',
        'importlib',
        'importlib.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['PyQt5', 'PySide6', 'PyQt6', 'PySide2'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='量化股票软件Tus',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('登录注册.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建PyInstaller配置文件: 登录注册.spec")

def build_login_exe():
    """构建登录程序exe文件"""
    print("开始构建登录程序exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "登录注册.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 登录程序exe文件构建成功！")
            print(f"输出目录: {os.path.abspath('dist')}")
            return True
        else:
            print("❌ 登录程序exe文件构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现异常: {str(e)}")
        return False

def copy_additional_files():
    """复制额外需要的文件到dist目录"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 需要复制的文件列表
    files_to_copy = [
        "requirements.txt",
        "tushare_token.txt",
    ]
    
    # 需要复制的目录
    dirs_to_copy = [
        "策略示例",
        "user_config",
        "market_data_cache",
        "drivers",
    ]
    
    print("正在复制额外文件...")
    
    # 复制文件
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"✓ 已复制: {file_name}")
            except Exception as e:
                print(f"⚠ 复制文件失败 {file_name}: {str(e)}")
    
    # 复制目录
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            try:
                dest_dir = dist_dir / dir_name
                if dest_dir.exists():
                    shutil.rmtree(dest_dir)
                shutil.copytree(dir_name, dest_dir)
                print(f"✓ 已复制目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 复制目录失败 {dir_name}: {str(e)}")
    
    return True

def create_usage_readme():
    """创建使用说明文件"""
    readme_content = f"""# 量化股票软件Tus - 使用说明

## 软件介绍
这是一个集成了登录注册和股票分析功能的量化交易软件。

### 主要功能
1. **用户登录注册**
   - 支持手机号注册和登录
   - 支持Token直接登录
   - 自动验证码识别和处理

2. **股票分析功能**
   - 技术指标分析（MACD、KDJ、缠论等）
   - 实时K线图显示
   - 策略回测功能

3. **量化交易**
   - 多种内置策略
   - 自定义策略编写
   - 自动化交易执行

## 使用方法

### 首次使用
1. 双击运行 `量化股票软件Tus.exe`
2. 选择登录方式：
   - **手机号登录**: 输入手机号，程序会自动处理验证码
   - **Token登录**: 直接输入Tushare Token码

### 登录成功后
- 程序会自动启动股票看图软件
- 可以进行股票分析、策略回测等操作

## 系统要求
- Windows 10/11 操作系统
- 已安装Edge或Chrome浏览器
- 稳定的网络连接

## 注意事项
1. 首次运行可能需要较长时间加载
2. 网页交易功能需要浏览器支持
3. 请确保网络连接正常
4. Token登录需要有效的Tushare Pro账户

## 故障排除
1. **程序无法启动**: 检查是否有杀毒软件拦截
2. **浏览器初始化失败**: 确认已安装Edge或Chrome浏览器
3. **网络连接问题**: 检查防火墙设置

## 技术支持
如有问题请联系技术支持。

## 版本信息
版本: v1.0
构建日期: {datetime.now().strftime("%Y-%m-%d")}
"""
    
    with open('使用说明.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 已创建使用说明.md文件")

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_clean = ['build', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 清理目录失败 {dir_name}: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("量化股票软件Tus - 登录程序打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('登录注册.py'):
        print("❌ 未找到主程序文件: 登录注册.py")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 检查依赖库
    missing_deps = check_dependencies()
    if missing_deps:
        if not install_dependencies(missing_deps):
            return False
    
    # 创建使用说明文件
    create_usage_readme()
    
    # 创建spec配置文件
    create_login_spec_file()
    
    # 构建exe文件
    if not build_login_exe():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "=" * 60)
    print("🎉 登录程序打包完成！")
    print("=" * 60)
    print(f"exe文件位置: {os.path.abspath('dist/量化股票软件Tus.exe')}")
    print(f"完整程序目录: {os.path.abspath('dist')}")
    print("\n使用说明:")
    print("1. 进入dist目录")
    print("2. 双击运行 量化股票软件Tus.exe")
    print("3. 选择登录方式完成登录")
    print("4. 登录成功后会自动启动股票分析软件")
    print("\n注意事项:")
    print("- 请确保目标电脑已安装Edge或Chrome浏览器")
    print("- 建议将整个dist目录复制到目标电脑")
    print("- 如遇到问题，请查看使用说明.md文件")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
