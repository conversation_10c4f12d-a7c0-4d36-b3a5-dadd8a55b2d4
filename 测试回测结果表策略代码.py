#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回测结果表中是否包含自定义策略代码
"""

import pandas as pd
import numpy as np
from datetime import datetime
from 使用者监控 import UserMonitor, set_user_phone, monitor_backtest_completion

def create_test_backtest_results():
    """创建测试回测结果"""
    return {
        'initial_capital': 100000,
        'final_value': 125000,
        'total_return': 0.25,
        'annual_return': 0.30,
        'volatility': 0.18,
        'sharpe_ratio': 1.5,
        'max_drawdown': -0.12,
        'total_trades': 20,
        'win_rate': 0.70
    }

def create_test_strategy_code():
    """创建测试策略代码"""
    return """
# RSI反转策略
# 基于RSI指标的超买超卖反转策略

# 计算RSI指标
def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

data['RSI'] = calculate_rsi(data['close'])

# 生成交易信号
data['signal'] = 0

# 买入条件：RSI < 30 (超卖)
buy_condition = data['RSI'] < 30
data.loc[buy_condition, 'signal'] = 1

# 卖出条件：RSI > 70 (超买)
sell_condition = data['RSI'] > 70
data.loc[sell_condition, 'signal'] = -1

# 计算持仓
data['position'] = data['signal'].shift(1).fillna(0)

print("RSI反转策略已应用")
print(f"买入信号数量: {(data['signal'] == 1).sum()}")
print(f"卖出信号数量: {(data['signal'] == -1).sum()}")
"""

def test_strategy_in_results_table():
    """测试回测结果表中的策略代码"""
    print("=" * 70)
    print("测试回测结果表中的自定义策略代码")
    print("=" * 70)
    
    # 1. 设置用户信息
    print("\n1. 设置用户信息...")
    test_phone = "19333264319"
    set_user_phone(test_phone)
    print(f"设置手机号: {test_phone}")
    
    # 2. 创建测试数据
    print("\n2. 创建测试数据...")
    backtest_results = create_test_backtest_results()
    strategy_code = create_test_strategy_code()
    print(f"策略代码长度: {len(strategy_code)} 字符")
    
    # 3. 测试监控系统
    print("\n3. 测试监控系统...")
    monitor = UserMonitor()
    
    # 收集数据
    collected_data = monitor.collect_backtest_data(backtest_results, strategy_code)
    print(f"收集的策略代码长度: {len(collected_data.get('custom_strategy_code', ''))}")
    
    # 生成XLSX文件
    filename = f"测试回测结果表_{datetime.now().strftime('%H%M%S')}.xlsx"
    filepath = monitor.create_xlsx_file(collected_data, filename)
    
    if filepath:
        print(f"XLSX文件生成成功: {filepath}")
        
        # 4. 验证回测结果表
        print("\n4. 验证回测结果表...")
        try:
            df_results = pd.read_excel(filepath, sheet_name='回测结果')
            print(f"回测结果表列名: {list(df_results.columns)}")
            
            # 检查是否包含自定义策略代码列
            if '自定义策略代码' in df_results.columns:
                strategy_in_results = df_results.iloc[0]['自定义策略代码']
                print(f"✅ 回测结果表包含自定义策略代码")
                print(f"   策略代码长度: {len(strategy_in_results)} 字符")
                print(f"   策略代码前100字符: {strategy_in_results[:100]}...")
                
                # 验证代码是否完整
                if strategy_in_results == strategy_code:
                    print("✅ 策略代码完全匹配")
                else:
                    print("❌ 策略代码不匹配")
            else:
                print("❌ 回测结果表不包含自定义策略代码列")
            
            # 显示回测结果表的所有数据
            print(f"\n回测结果表数据:")
            for col in df_results.columns:
                value = df_results.iloc[0][col]
                if col == '自定义策略代码':
                    print(f"  {col}: (长度: {len(str(value))} 字符)")
                elif isinstance(value, float):
                    print(f"  {col}: {value:.4f}")
                else:
                    print(f"  {col}: {value}")
                    
        except Exception as e:
            print(f"验证回测结果表失败: {e}")
        
        # 5. 验证自定义公式表
        print("\n5. 验证自定义公式表...")
        try:
            df_strategy = pd.read_excel(filepath, sheet_name='自定义公式')
            print(f"自定义公式表列名: {list(df_strategy.columns)}")
            
            strategy_in_formula = df_strategy.iloc[0]['自定义策略代码']
            print(f"自定义公式表策略代码长度: {len(strategy_in_formula)} 字符")
            
            # 验证两个表中的策略代码是否一致
            if '自定义策略代码' in df_results.columns:
                strategy_in_results = df_results.iloc[0]['自定义策略代码']
                if strategy_in_results == strategy_in_formula:
                    print("✅ 两个表中的策略代码一致")
                else:
                    print("❌ 两个表中的策略代码不一致")
                    
        except Exception as e:
            print(f"验证自定义公式表失败: {e}")
    
    else:
        print("❌ XLSX文件生成失败")
    
    # 6. 测试完整监控流程
    print("\n6. 测试完整监控流程...")
    success = monitor_backtest_completion(backtest_results, strategy_code)
    
    if success:
        print("✅ 完整监控流程测试成功")
        
        # 查找最新生成的文件
        import os
        config_dir = "user_config"
        xlsx_files = [f for f in os.listdir(config_dir) if f.endswith('.xlsx')]
        if xlsx_files:
            latest_file = max(xlsx_files, key=lambda x: os.path.getctime(os.path.join(config_dir, x)))
            latest_path = os.path.join(config_dir, latest_file)
            
            print(f"\n最新生成的文件: {latest_file}")
            
            # 验证最新文件的回测结果表
            try:
                df_latest = pd.read_excel(latest_path, sheet_name='回测结果')
                print(f"最新文件回测结果表列名: {list(df_latest.columns)}")
                
                if '自定义策略代码' in df_latest.columns:
                    latest_strategy = df_latest.iloc[0]['自定义策略代码']
                    print(f"✅ 最新文件包含策略代码 (长度: {len(latest_strategy)} 字符)")
                else:
                    print("❌ 最新文件不包含策略代码")
                    
            except Exception as e:
                print(f"验证最新文件失败: {e}")
    else:
        print("❌ 完整监控流程测试失败")
    
    print("\n" + "=" * 70)
    print("回测结果表策略代码测试完成")
    print("=" * 70)

if __name__ == "__main__":
    test_strategy_in_results_table()
