#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包结果脚本
验证生成的EXE文件是否包含所有必要的文件和功能
"""

import os
import sys
from pathlib import Path

def check_exe_file():
    """检查EXE文件是否存在"""
    exe_path = Path("dist/量化股票软件Tus.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✓ EXE文件存在: {exe_path}")
        print(f"  文件大小: {size_mb:.1f} MB")
        return True
    else:
        print("❌ EXE文件不存在")
        return False

def check_required_files():
    """检查必要的文件是否都被复制到dist目录"""
    dist_dir = Path("dist")
    
    required_files = [
        "requirements.txt",
        "tushare_token.txt",
    ]
    
    required_dirs = [
        "策略示例",
        "user_config", 
        "market_data_cache",
        "drivers",
    ]
    
    print("\n检查必要文件:")
    all_good = True
    
    # 检查文件
    for file_name in required_files:
        file_path = dist_dir / file_name
        if file_path.exists():
            print(f"✓ {file_name}")
        else:
            print(f"❌ {file_name} 缺失")
            all_good = False
    
    # 检查目录
    for dir_name in required_dirs:
        dir_path = dist_dir / dir_name
        if dir_path.exists() and dir_path.is_dir():
            file_count = len(list(dir_path.rglob("*")))
            print(f"✓ {dir_name}/ ({file_count} 个文件)")
        else:
            print(f"❌ {dir_name}/ 目录缺失")
            all_good = False
    
    return all_good

def check_strategy_files():
    """检查策略示例文件"""
    strategy_dir = Path("dist/策略示例")
    if not strategy_dir.exists():
        print("❌ 策略示例目录不存在")
        return False
    
    strategy_files = list(strategy_dir.glob("*.py"))
    print(f"\n策略示例文件 ({len(strategy_files)} 个):")
    for file_path in strategy_files:
        print(f"  ✓ {file_path.name}")
    
    return len(strategy_files) > 0

def check_data_cache():
    """检查数据缓存文件"""
    cache_dir = Path("dist/market_data_cache")
    if not cache_dir.exists():
        print("❌ 市场数据缓存目录不存在")
        return False
    
    cache_files = list(cache_dir.glob("*.pkl.gz"))
    print(f"\n数据缓存文件 ({len(cache_files)} 个):")
    total_size = 0
    for file_path in cache_files[:5]:  # 只显示前5个
        size_kb = file_path.stat().st_size / 1024
        total_size += size_kb
        print(f"  ✓ {file_path.name} ({size_kb:.1f} KB)")
    
    if len(cache_files) > 5:
        print(f"  ... 还有 {len(cache_files) - 5} 个文件")
    
    print(f"  总大小: {total_size / 1024:.1f} MB")
    return len(cache_files) > 0

def check_user_config():
    """检查用户配置文件"""
    config_dir = Path("dist/user_config")
    if not config_dir.exists():
        print("❌ 用户配置目录不存在")
        return False
    
    config_file = config_dir / "user_config.json"
    xlsx_files = list(config_dir.glob("*.xlsx"))
    
    print(f"\n用户配置:")
    if config_file.exists():
        print(f"  ✓ user_config.json")
    else:
        print(f"  ⚠ user_config.json 不存在（首次运行时会创建）")
    
    print(f"  ✓ 回测数据文件: {len(xlsx_files)} 个")
    
    return True

def create_test_report():
    """创建测试报告"""
    report_content = """# 打包测试报告

## 测试时间
{datetime}

## 测试结果总结
{summary}

## 详细检查结果

### 1. EXE文件检查
{exe_check}

### 2. 必要文件检查  
{files_check}

### 3. 策略文件检查
{strategy_check}

### 4. 数据缓存检查
{cache_check}

### 5. 用户配置检查
{config_check}

## 使用建议
1. 将整个dist目录复制到目标电脑
2. 确保目标电脑已安装Edge或Chrome浏览器
3. 首次运行可能需要较长时间加载
4. 如遇到问题，检查防火墙和杀毒软件设置

## 注意事项
- 程序需要网络连接才能正常工作
- Token登录需要有效的Tushare Pro账户
- 自动交易功能请谨慎使用
"""
    
    from datetime import datetime
    
    # 执行各项检查
    exe_ok = check_exe_file()
    files_ok = check_required_files()
    strategy_ok = check_strategy_files()
    cache_ok = check_data_cache()
    config_ok = check_user_config()
    
    # 生成总结
    all_checks = [exe_ok, files_ok, strategy_ok, cache_ok, config_ok]
    passed_count = sum(all_checks)
    total_count = len(all_checks)
    
    if passed_count == total_count:
        summary = f"✅ 所有检查通过 ({passed_count}/{total_count})"
    else:
        summary = f"⚠️ 部分检查未通过 ({passed_count}/{total_count})"
    
    # 填充报告内容
    report = report_content.format(
        datetime=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        summary=summary,
        exe_check="✅ 通过" if exe_ok else "❌ 失败",
        files_check="✅ 通过" if files_ok else "❌ 失败", 
        strategy_check="✅ 通过" if strategy_ok else "❌ 失败",
        cache_check="✅ 通过" if cache_ok else "❌ 失败",
        config_check="✅ 通过" if config_ok else "❌ 失败"
    )
    
    # 保存报告
    with open("打包测试报告.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📋 测试报告已保存: 打包测试报告.md")
    return passed_count == total_count

def main():
    """主函数"""
    print("=" * 60)
    print("量化股票软件Tus - 打包结果测试")
    print("=" * 60)
    
    if not os.path.exists("dist"):
        print("❌ dist目录不存在，请先运行打包脚本")
        return False
    
    # 执行测试并生成报告
    success = create_test_report()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！打包结果良好")
        print("可以安全地将dist目录复制到目标电脑使用")
    else:
        print("⚠️ 部分测试未通过，请检查打包过程")
        print("建议重新运行打包脚本")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n测试{'成功' if success else '失败'}，按回车键退出...")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        input("按回车键退出...")
