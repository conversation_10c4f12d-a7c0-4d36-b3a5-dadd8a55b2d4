"""
市场数据管理功能演示脚本
展示如何使用市场数据管理器的各种功能
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 市场数据管理 import MarketDataManager

def demo_basic_functions():
    """演示基本功能"""
    print("🚀 市场数据管理功能演示")
    print("=" * 60)
    
    # 初始化管理器
    token = '8d64d13830ee4aaacbbcdad1d27e109a66d6e77cddb96ff3ea6fa449'
    manager = MarketDataManager(cache_dir="demo_cache", token=token)
    
    print("✅ 市场数据管理器初始化完成")
    print()
    
    # 1. 获取股票列表
    print("📊 1. 获取股票列表")
    print("-" * 30)
    stock_list = manager.get_all_stock_list()
    print(f"✅ 获取到 {len(stock_list)} 只股票")
    
    # 显示前10只股票
    print("\n前10只股票:")
    for i, (_, row) in enumerate(stock_list.head(10).iterrows()):
        print(f"{i+1:2d}. {row['ts_code']} - {row['name']:8s} ({row.get('sector', 'N/A')})")
    
    print()
    
    # 2. 板块分类统计
    print("🏢 2. 板块分类统计")
    print("-" * 30)
    sector_summary = manager.get_sector_summary()
    for sector, info in sector_summary.items():
        print(f"{sector:8s}: {info['count']:4d} 只股票")
    
    print()
    
    # 3. 市场分类统计
    print("🏛️ 3. 市场分类统计")
    print("-" * 30)
    market_summary = manager.get_market_summary()
    for market, info in market_summary.items():
        print(f"{market:12s}: {info['count']:4d} 只股票")
    
    print()
    
    # 4. 搜索功能演示
    print("🔍 4. 搜索功能演示")
    print("-" * 30)
    
    # 搜索银行股
    bank_stocks = manager.search_stocks("银行", "name")
    print(f"搜索'银行': {len(bank_stocks)} 只股票")
    for _, row in bank_stocks.head(5).iterrows():
        print(f"  {row['ts_code']} - {row['name']}")
    
    print()
    
    # 搜索科技股
    tech_stocks = manager.search_stocks("科技", "name")
    print(f"搜索'科技': {len(tech_stocks)} 只股票")
    for _, row in tech_stocks.head(5).iterrows():
        print(f"  {row['ts_code']} - {row['name']}")
    
    print()
    
    # 5. 获取特定板块股票
    print("📈 5. 获取特定板块股票")
    print("-" * 30)
    
    gem_stocks = manager.get_sector_stocks("创业板")
    print(f"创业板股票: {len(gem_stocks)} 只")
    print("前5只创业板股票:")
    for i, code in enumerate(gem_stocks[:5]):
        stock_info = stock_list[stock_list['ts_code'] == code]
        if not stock_info.empty:
            name = stock_info.iloc[0]['name']
            print(f"  {i+1}. {code} - {name}")
    
    print()
    
    # 6. 获取股票详细信息
    print("📋 6. 获取股票详细信息")
    print("-" * 30)
    
    test_code = "000001.SZ"
    stock_info = manager.get_stock_info(test_code)
    if stock_info:
        print(f"股票代码: {stock_info['ts_code']}")
        print(f"股票名称: {stock_info['name']}")
        print(f"所属板块: {stock_info.get('sector', 'N/A')}")
        print(f"所属市场: {stock_info.get('market_name', 'N/A')}")
        print(f"所属行业: {stock_info.get('industry', 'N/A')}")
        print(f"所属地区: {stock_info.get('area', 'N/A')}")
        print(f"上市日期: {stock_info.get('list_date', 'N/A')}")
    
    print()
    
    # 7. 获取历史数据
    print("📊 7. 获取历史数据")
    print("-" * 30)
    
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    end_date = datetime.now().strftime('%Y%m%d')
    
    daily_data = manager.get_stock_daily_data(test_code, start_date, end_date)
    if not daily_data.empty:
        print(f"获取到 {test_code} 最近30天的 {len(daily_data)} 条数据")
        latest = daily_data.iloc[-1]
        print(f"最新数据: {latest['trade_date'].strftime('%Y-%m-%d')}")
        print(f"  开盘价: {latest['open']:.2f}")
        print(f"  最高价: {latest['high']:.2f}")
        print(f"  最低价: {latest['low']:.2f}")
        print(f"  收盘价: {latest['close']:.2f}")
        print(f"  成交量: {latest['vol']:,.0f}")
    
    print()
    
    # 8. 缓存信息
    print("💾 8. 缓存信息")
    print("-" * 30)
    
    cache_info = manager.get_cache_info()
    print(f"缓存目录: {cache_info['cache_dir']}")
    print(f"缓存文件数: {cache_info['total_files']}")
    print(f"缓存总大小: {cache_info['total_size'] / 1024 / 1024:.2f} MB")
    
    if cache_info['files']:
        print("\n缓存文件列表:")
        for file_info in cache_info['files'][:5]:
            print(f"  {file_info['name']} ({file_info['size'] / 1024:.1f} KB)")
    
    print()
    print("🎉 演示完成!")

def demo_advanced_features():
    """演示高级功能"""
    print("\n" + "=" * 60)
    print("🔬 高级功能演示")
    print("=" * 60)
    
    token = '8d64d13830ee4aaacbbcdad1d27e109a66d6e77cddb96ff3ea6fa449'
    manager = MarketDataManager(cache_dir="demo_cache", token=token)
    
    # 1. 板块对比分析
    print("📊 1. 板块对比分析")
    print("-" * 30)
    
    sectors = ["主板", "创业板", "科创板"]
    for sector in sectors:
        stocks = manager.get_sector_stocks(sector)
        print(f"{sector:8s}: {len(stocks):4d} 只股票")
    
    print()
    
    # 2. 市场分布分析
    print("🏛️ 2. 市场分布分析")
    print("-" * 30)
    
    stock_list = manager.get_all_stock_list()
    
    # 按地区统计
    area_stats = stock_list['area'].value_counts().head(10)
    print("前10个地区股票数量:")
    for area, count in area_stats.items():
        print(f"  {area:8s}: {count:4d} 只")
    
    print()
    
    # 按行业统计
    industry_stats = stock_list['industry'].value_counts().head(10)
    print("前10个行业股票数量:")
    for industry, count in industry_stats.items():
        print(f"  {industry:12s}: {count:4d} 只")
    
    print()
    
    # 3. 数据质量检查
    print("🔍 3. 数据质量检查")
    print("-" * 30)
    
    total_stocks = len(stock_list)
    missing_industry = stock_list['industry'].isna().sum()
    missing_area = stock_list['area'].isna().sum()
    
    print(f"总股票数: {total_stocks}")
    print(f"缺失行业信息: {missing_industry} ({missing_industry/total_stocks*100:.1f}%)")
    print(f"缺失地区信息: {missing_area} ({missing_area/total_stocks*100:.1f}%)")
    
    print()
    print("🎯 高级功能演示完成!")

if __name__ == "__main__":
    try:
        # 运行基本功能演示
        demo_basic_functions()
        
        # 运行高级功能演示
        demo_advanced_features()
        
        print("\n" + "=" * 60)
        print("✨ 所有演示完成! 市场数据管理功能运行正常。")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n💡 提示: 可以运行 '股票看图软件_增强版.py' 来体验完整的图形界面功能。")
