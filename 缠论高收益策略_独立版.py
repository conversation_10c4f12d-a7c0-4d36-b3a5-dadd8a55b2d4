"""
缠论高收益策略 - 独立版本
基于缠论分析波段结构、中枢、支撑阻力位，实现高年化收益率的交易策略
可直接导入使用，支持策略优化和迭代
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入技术指标库
from 技术指标库 import *

class ChanlunAdvancedStrategy:
    """缠论高级策略类"""
    
    def __init__(self, optimize_mode=False):
        self.name = "缠论高收益策略"
        self.optimize_mode = optimize_mode
        
        # 策略参数 - 可优化
        self.params = {
            # 缠论核心参数
            'fenxing_window': 3,           # 分型识别窗口
            'fenxing_strength_min': 1.5,   # 分型强度阈值
            'bi_min_length': 0.015,        # 笔的最小长度(价格比例)
            'zhongshu_overlap_min': 0.3,   # 中枢重叠最小比例
            
            # 技术指标参数
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'ma_fast': 5,
            'ma_slow': 20,
            'ma_trend': 60,
            
            # 交易参数
            'stop_loss': 0.03,             # 止损3%
            'take_profit': 0.08,           # 止盈8%
            'trailing_stop': 0.02,         # 移动止损2%
            'volume_threshold': 1.2,       # 成交量确认倍数
            
            # 买入条件权重
            'weight_support': 2.0,         # 支撑位权重
            'weight_zhongshu': 2.5,        # 中枢权重
            'weight_trend': 1.5,           # 趋势权重
            'weight_technical': 1.0,       # 技术指标权重
            'weight_volume': 1.0,          # 成交量权重
            
            # 风险控制
            'max_hold_days': 25,           # 最大持仓天数
            'min_trade_interval': 3,       # 最小交易间隔
            'position_size': 0.9,          # 仓位大小
        }
        
        # 状态变量
        self.reset_state()
        
    def reset_state(self):
        """重置策略状态"""
        self.position = 0
        self.entry_price = 0
        self.entry_index = 0
        self.highest_price = 0
        self.trade_count = 0
        self.last_trade_index = 0
        
    def identify_fenxing(self, data: pd.DataFrame) -> List[Dict]:
        """识别分型点 - 缠论核心"""
        fenxing_points = []
        window = self.params['fenxing_window']
        
        for i in range(window, len(data) - window):
            # 顶分型判断
            is_top = True
            current_high = data['high'].iloc[i]
            
            for j in range(i - window, i + window + 1):
                if j != i and data['high'].iloc[j] >= current_high:
                    is_top = False
                    break
            
            if is_top:
                strength = self.calculate_fenxing_strength(data, i, 'top')
                if strength >= self.params['fenxing_strength_min']:
                    fenxing_points.append({
                        'index': i,
                        'type': 'top',
                        'price': current_high,
                        'strength': strength
                    })
            
            # 底分型判断
            is_bottom = True
            current_low = data['low'].iloc[i]
            
            for j in range(i - window, i + window + 1):
                if j != i and data['low'].iloc[j] <= current_low:
                    is_bottom = False
                    break
            
            if is_bottom:
                strength = self.calculate_fenxing_strength(data, i, 'bottom')
                if strength >= self.params['fenxing_strength_min']:
                    fenxing_points.append({
                        'index': i,
                        'type': 'bottom',
                        'price': current_low,
                        'strength': strength
                    })
        
        return sorted(fenxing_points, key=lambda x: x['index'])
    
    def calculate_fenxing_strength(self, data: pd.DataFrame, index: int, fenxing_type: str) -> float:
        """计算分型强度"""
        window = 10
        start = max(0, index - window)
        end = min(len(data), index + window + 1)
        
        if fenxing_type == 'top':
            current_price = data['high'].iloc[index]
            nearby_prices = data['high'].iloc[start:end]
        else:
            current_price = data['low'].iloc[index]
            nearby_prices = data['low'].iloc[start:end]
        
        if nearby_prices.std() > 0:
            if fenxing_type == 'top':
                strength = (current_price - nearby_prices.mean()) / nearby_prices.std()
            else:
                strength = (nearby_prices.mean() - current_price) / nearby_prices.std()
        else:
            strength = 1.0
        
        return max(1.0, strength)
    
    def construct_bi(self, data: pd.DataFrame, fenxing_points: List[Dict]) -> List[Dict]:
        """构造笔"""
        if len(fenxing_points) < 2:
            return []
        
        bi_lines = []
        i = 0
        
        while i < len(fenxing_points) - 1:
            start_point = fenxing_points[i]
            
            # 寻找下一个不同类型的分型
            j = i + 1
            while j < len(fenxing_points) and fenxing_points[j]['type'] == start_point['type']:
                # 同类型分型，选择更极端的
                if start_point['type'] == 'top':
                    if fenxing_points[j]['price'] > start_point['price']:
                        start_point = fenxing_points[j]
                        i = j
                else:
                    if fenxing_points[j]['price'] < start_point['price']:
                        start_point = fenxing_points[j]
                        i = j
                j += 1
            
            if j < len(fenxing_points):
                end_point = fenxing_points[j]
                
                # 验证笔的有效性
                if self.is_valid_bi(start_point, end_point):
                    bi_lines.append({
                        'start': start_point,
                        'end': end_point,
                        'direction': 'up' if start_point['type'] == 'bottom' else 'down',
                        'length': abs(end_point['price'] - start_point['price']),
                        'strength': (start_point['strength'] + end_point['strength']) / 2
                    })
                
                i = j
            else:
                break
        
        return bi_lines
    
    def is_valid_bi(self, start: Dict, end: Dict) -> bool:
        """验证笔的有效性"""
        price_change = abs(end['price'] - start['price'])
        avg_price = (start['price'] + end['price']) / 2
        return price_change / avg_price >= self.params['bi_min_length']
    
    def identify_zhongshu(self, bi_lines: List[Dict]) -> List[Dict]:
        """识别中枢"""
        if len(bi_lines) < 3:
            return []
        
        zhongshu_list = []
        
        for i in range(len(bi_lines) - 2):
            bi1, bi2, bi3 = bi_lines[i], bi_lines[i+1], bi_lines[i+2]
            
            # 获取三笔的价格区间
            prices1 = [bi1['start']['price'], bi1['end']['price']]
            prices2 = [bi2['start']['price'], bi2['end']['price']]
            prices3 = [bi3['start']['price'], bi3['end']['price']]
            
            # 计算重叠区间
            zone_high = min(max(prices1), max(prices2), max(prices3))
            zone_low = max(min(prices1), min(prices2), min(prices3))
            
            if zone_high > zone_low:
                # 计算重叠比例
                total_range = max(max(prices1), max(prices2), max(prices3)) - min(min(prices1), min(prices2), min(prices3))
                overlap_ratio = (zone_high - zone_low) / total_range
                
                if overlap_ratio >= self.params['zhongshu_overlap_min']:
                    zhongshu_list.append({
                        'start_index': bi1['start']['index'],
                        'end_index': bi3['end']['index'],
                        'zone_high': zone_high,
                        'zone_low': zone_low,
                        'zone_mid': (zone_high + zone_low) / 2,
                        'strength': (bi1['strength'] + bi2['strength'] + bi3['strength']) / 3,
                        'type': self.classify_zhongshu_direction(bi1, bi3)
                    })
        
        return zhongshu_list
    
    def classify_zhongshu_direction(self, first_bi: Dict, last_bi: Dict) -> str:
        """判断中枢方向"""
        if first_bi['direction'] == 'up' and last_bi['direction'] == 'up':
            return 'upward'
        elif first_bi['direction'] == 'down' and last_bi['direction'] == 'down':
            return 'downward'
        else:
            return 'sideways'
    
    def find_support_resistance(self, fenxing_points: List[Dict], current_index: int) -> Dict:
        """寻找支撑阻力位"""
        recent_fenxing = [f for f in fenxing_points if f['index'] <= current_index]
        
        # 最近的顶部和底部
        tops = [f for f in recent_fenxing if f['type'] == 'top'][-10:]
        bottoms = [f for f in recent_fenxing if f['type'] == 'bottom'][-10:]
        
        # 按强度排序
        resistance_levels = sorted(tops, key=lambda x: x['strength'], reverse=True)[:5]
        support_levels = sorted(bottoms, key=lambda x: x['strength'], reverse=True)[:5]
        
        return {
            'resistance': resistance_levels,
            'support': support_levels
        }
    
    def analyze_trend_structure(self, data: pd.DataFrame, bi_lines: List[Dict], current_index: int) -> Dict:
        """分析趋势结构"""
        if not bi_lines:
            return {'trend': 'unknown', 'strength': 0}
        
        # 最近的笔
        recent_bi = [bi for bi in bi_lines if bi['end']['index'] <= current_index][-5:]
        
        if not recent_bi:
            return {'trend': 'unknown', 'strength': 0}
        
        # 统计方向
        up_count = sum(1 for bi in recent_bi if bi['direction'] == 'up')
        down_count = len(recent_bi) - up_count
        
        if up_count > down_count:
            trend = 'uptrend'
            strength = (up_count / len(recent_bi)) * 100
        elif down_count > up_count:
            trend = 'downtrend'
            strength = (down_count / len(recent_bi)) * 100
        else:
            trend = 'sideways'
            strength = 50
        
        return {'trend': trend, 'strength': strength}
    
    def generate_buy_signal(self, data: pd.DataFrame, index: int, 
                           fenxing_points: List[Dict], bi_lines: List[Dict], 
                           zhongshu_list: List[Dict]) -> Tuple[bool, float]:
        """生成买入信号和信号强度"""
        if self.position != 0:
            return False, 0
        
        # 检查交易间隔
        if index - self.last_trade_index < self.params['min_trade_interval']:
            return False, 0
        
        current_price = data['close'].iloc[index]
        signal_strength = 0
        
        # 1. 支撑位分析
        support_resistance = self.find_support_resistance(fenxing_points, index)
        for support in support_resistance['support']:
            if abs(current_price - support['price']) / support['price'] < 0.02:
                signal_strength += self.params['weight_support'] * support['strength']
        
        # 2. 中枢分析
        for zhongshu in zhongshu_list:
            if (zhongshu['end_index'] >= index - 10 and 
                current_price <= zhongshu['zone_low'] * 1.01):
                signal_strength += self.params['weight_zhongshu'] * zhongshu['strength']
        
        # 3. 趋势结构分析
        trend_info = self.analyze_trend_structure(data, bi_lines, index)
        if trend_info['trend'] == 'uptrend' and trend_info['strength'] > 60:
            signal_strength += self.params['weight_trend'] * (trend_info['strength'] / 100)
        
        # 4. 技术指标确认
        rsi = RSI(data['close'], self.params['rsi_period'])
        ma_fast = SMA(data['close'], self.params['ma_fast'])
        ma_slow = SMA(data['close'], self.params['ma_slow'])
        dif, dea, macd = MACD(data['close'], 12, 26, 9)
        
        if (index >= self.params['rsi_period'] and 
            pd.notna(rsi.iloc[index]) and pd.notna(ma_fast.iloc[index])):
            
            # RSI超卖反弹
            if rsi.iloc[index] < self.params['rsi_oversold'] and rsi.iloc[index] > rsi.iloc[index-1]:
                signal_strength += self.params['weight_technical']
            
            # 均线多头排列
            if ma_fast.iloc[index] > ma_slow.iloc[index]:
                signal_strength += self.params['weight_technical']
            
            # MACD金叉
            if (pd.notna(dif.iloc[index]) and pd.notna(dea.iloc[index]) and
                dif.iloc[index] > dea.iloc[index] and dif.iloc[index-1] <= dea.iloc[index-1]):
                signal_strength += self.params['weight_technical']
        
        # 5. 成交量确认
        if 'vol' in data.columns and index >= 20:
            recent_vol = data['vol'].iloc[max(0, index-5):index+1].mean()
            avg_vol = data['vol'].iloc[max(0, index-20):index].mean()
            if recent_vol > avg_vol * self.params['volume_threshold']:
                signal_strength += self.params['weight_volume']
        
        # 买入决策：信号强度大于阈值
        return signal_strength >= 4.0, signal_strength
    
    def generate_sell_signal(self, data: pd.DataFrame, index: int,
                            fenxing_points: List[Dict], bi_lines: List[Dict],
                            zhongshu_list: List[Dict]) -> Tuple[bool, str]:
        """生成卖出信号"""
        if self.position == 0:
            return False, ""
        
        current_price = data['close'].iloc[index]
        
        # 更新最高价
        self.highest_price = max(self.highest_price, current_price)
        
        # 1. 止损止盈
        if current_price <= self.entry_price * (1 - self.params['stop_loss']):
            return True, "止损"
        
        if current_price >= self.entry_price * (1 + self.params['take_profit']):
            return True, "止盈"
        
        if current_price <= self.highest_price * (1 - self.params['trailing_stop']):
            return True, "移动止损"
        
        # 2. 最大持仓时间
        if index - self.entry_index >= self.params['max_hold_days']:
            return True, "超时"
        
        # 3. 阻力位卖出
        support_resistance = self.find_support_resistance(fenxing_points, index)
        for resistance in support_resistance['resistance']:
            if abs(current_price - resistance['price']) / resistance['price'] < 0.02:
                return True, "阻力位"
        
        # 4. 中枢上沿卖出
        for zhongshu in zhongshu_list:
            if (zhongshu['end_index'] >= index - 10 and
                current_price >= zhongshu['zone_high'] * 0.99):
                return True, "中枢上沿"
        
        # 5. 技术指标卖出
        rsi = RSI(data['close'], self.params['rsi_period'])
        if (index >= self.params['rsi_period'] and pd.notna(rsi.iloc[index]) and
            rsi.iloc[index] > self.params['rsi_overbought']):
            
            # 趋势转弱确认
            trend_info = self.analyze_trend_structure(data, bi_lines, index)
            if trend_info['trend'] == 'downtrend' and trend_info['strength'] > 60:
                return True, "趋势转弱"
        
        return False, ""
    
    def run_strategy(self, data: pd.DataFrame) -> Dict:
        """运行策略"""
        self.reset_state()
        
        # 分析缠论结构
        fenxing_points = self.identify_fenxing(data)
        bi_lines = self.construct_bi(data, fenxing_points)
        zhongshu_list = self.identify_zhongshu(bi_lines)
        
        # 初始化结果
        signals = [0] * len(data)
        positions = [0] * len(data)
        trades = []
        
        # 遍历数据生成信号
        for i in range(60, len(data)):  # 从第60个数据点开始
            current_price = data['close'].iloc[i]
            
            # 生成买入信号
            if self.position == 0:
                buy_signal, signal_strength = self.generate_buy_signal(
                    data, i, fenxing_points, bi_lines, zhongshu_list)
                
                if buy_signal:
                    signals[i] = 1
                    self.position = 1
                    self.entry_price = current_price
                    self.entry_index = i
                    self.highest_price = current_price
                    self.trade_count += 1
            
            # 生成卖出信号
            elif self.position == 1:
                sell_signal, sell_reason = self.generate_sell_signal(
                    data, i, fenxing_points, bi_lines, zhongshu_list)
                
                if sell_signal:
                    signals[i] = -1
                    
                    # 记录交易
                    trade_return = (current_price - self.entry_price) / self.entry_price
                    trades.append({
                        'entry_index': self.entry_index,
                        'exit_index': i,
                        'entry_price': self.entry_price,
                        'exit_price': current_price,
                        'return': trade_return,
                        'hold_days': i - self.entry_index,
                        'exit_reason': sell_reason
                    })
                    
                    self.position = 0
                    self.last_trade_index = i
            
            positions[i] = self.position
        
        # 计算策略表现
        total_return = sum([trade['return'] for trade in trades])
        win_trades = [t for t in trades if t['return'] > 0]
        win_rate = len(win_trades) / len(trades) if trades else 0
        
        # 计算年化收益率
        if len(data) > 0 and 'trade_date' in data.columns:
            try:
                start_date = pd.to_datetime(str(data['trade_date'].iloc[0]))
                end_date = pd.to_datetime(str(data['trade_date'].iloc[-1]))
                years = (end_date - start_date).days / 365.25
                if years > 0 and total_return > -0.99:  # 避免负数开方
                    annual_return = (1 + total_return) ** (1/years) - 1
                else:
                    annual_return = total_return / max(1, years) if years > 0 else 0
            except Exception as e:
                # 简单计算：假设数据跨度为总天数
                days = len(data)
                years = days / 252  # 交易日年化
                annual_return = total_return / max(1, years) if years > 0 else 0
        else:
            annual_return = 0
        
        return {
            'signals': signals,
            'positions': positions,
            'trades': trades,
            'total_return': total_return,
            'annual_return': annual_return,
            'win_rate': win_rate,
            'total_trades': len(trades),
            'fenxing_count': len(fenxing_points),
            'bi_count': len(bi_lines),
            'zhongshu_count': len(zhongshu_list),
            'strategy_params': self.params.copy()
        }

def load_and_test_strategy(file_path='梦百合.xlsx'):
    """加载数据并测试策略"""
    try:
        # 加载数据
        df = pd.read_excel(file_path)
        df = df.sort_values('trade_date').reset_index(drop=True)
        
        if '价格' in df.columns:
            df['close'] = df['价格']
        
        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close', 'vol']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna(subset=['close'])
        
        print(f"数据加载成功，共{len(df)}条记录")
        print(f"数据时间范围: {df['trade_date'].min()} - {df['trade_date'].max()}")
        
        # 运行策略
        strategy = ChanlunAdvancedStrategy()
        results = strategy.run_strategy(df)
        
        # 输出结果
        print(f"\n=== 缠论高收益策略回测结果 ===")
        print(f"总交易次数: {results['total_trades']}")
        print(f"总收益率: {results['total_return']:.2%}")
        print(f"年化收益率: {results['annual_return']:.2%}")
        print(f"胜率: {results['win_rate']:.2%}")
        print(f"识别分型: {results['fenxing_count']}个")
        print(f"构造笔: {results['bi_count']}条")
        print(f"识别中枢: {results['zhongshu_count']}个")
        
        # 分析交易详情
        if results['trades']:
            avg_return = results['total_return'] / results['total_trades']
            avg_hold_days = sum([t['hold_days'] for t in results['trades']]) / len(results['trades'])
            print(f"平均每笔收益: {avg_return:.2%}")
            print(f"平均持仓天数: {avg_hold_days:.1f}天")
            
            # 按退出原因统计
            exit_reasons = {}
            for trade in results['trades']:
                reason = trade['exit_reason']
                exit_reasons[reason] = exit_reasons.get(reason, 0) + 1
            
            print(f"\n退出原因统计:")
            for reason, count in exit_reasons.items():
                print(f"  {reason}: {count}次")
        
        return results
        
    except Exception as e:
        print(f"策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

class StrategyOptimizer:
    """策略参数优化器"""

    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.best_params = None
        self.best_annual_return = -999
        self.optimization_history = []

    def optimize_parameters(self, max_iterations=50):
        """优化策略参数"""
        print("开始策略参数优化...")

        # 定义参数优化范围
        param_ranges = {
            'fenxing_strength_min': [1.0, 1.5, 2.0, 2.5],
            'rsi_oversold': [25, 30, 35, 40],
            'rsi_overbought': [60, 65, 70, 75],
            'stop_loss': [0.02, 0.03, 0.04, 0.05],
            'take_profit': [0.06, 0.08, 0.10, 0.12],
            'trailing_stop': [0.015, 0.02, 0.025, 0.03],
            'volume_threshold': [1.0, 1.2, 1.5, 1.8],
            'weight_support': [1.5, 2.0, 2.5, 3.0],
            'weight_zhongshu': [2.0, 2.5, 3.0, 3.5]
        }

        iteration = 0

        # 网格搜索优化
        import itertools

        # 为了避免组合爆炸，我们分批优化关键参数
        key_params = ['rsi_oversold', 'rsi_overbought', 'stop_loss', 'take_profit']

        for values in itertools.product(*[param_ranges[param] for param in key_params]):
            if iteration >= max_iterations:
                break

            # 创建参数组合
            test_params = dict(zip(key_params, values))

            # 测试参数组合
            annual_return = self.test_parameter_combination(test_params)

            # 记录结果
            self.optimization_history.append({
                'iteration': iteration,
                'params': test_params.copy(),
                'annual_return': annual_return
            })

            # 更新最佳参数
            if annual_return > self.best_annual_return:
                self.best_annual_return = annual_return
                self.best_params = test_params.copy()
                print(f"迭代 {iteration}: 发现更好参数，年化收益率: {annual_return:.2%}")

            iteration += 1

        print(f"\n优化完成！最佳年化收益率: {self.best_annual_return:.2%}")
        print(f"最佳参数组合: {self.best_params}")

        return self.best_params, self.best_annual_return

    def test_parameter_combination(self, test_params: Dict) -> float:
        """测试参数组合"""
        try:
            strategy = ChanlunAdvancedStrategy()

            # 更新参数
            for param, value in test_params.items():
                strategy.params[param] = value

            # 运行策略
            results = strategy.run_strategy(self.data)

            return results['annual_return']

        except Exception as e:
            return -999  # 返回极低值表示失败

    def analyze_optimization_results(self):
        """分析优化结果"""
        if not self.optimization_history:
            print("没有优化历史数据")
            return

        # 按年化收益率排序
        sorted_results = sorted(self.optimization_history,
                               key=lambda x: x['annual_return'], reverse=True)

        print(f"\n=== 优化结果分析 ===")
        print(f"总测试次数: {len(self.optimization_history)}")
        print(f"最佳年化收益率: {sorted_results[0]['annual_return']:.2%}")
        print(f"最差年化收益率: {sorted_results[-1]['annual_return']:.2%}")

        # 显示前5个最佳结果
        print(f"\n前5个最佳参数组合:")
        for i, result in enumerate(sorted_results[:5]):
            print(f"{i+1}. 年化收益率: {result['annual_return']:.2%}")
            print(f"   参数: {result['params']}")

        return sorted_results

def run_strategy_optimization(file_path='梦百合.xlsx'):
    """运行策略优化"""
    try:
        # 加载数据
        df = pd.read_excel(file_path)
        df = df.sort_values('trade_date').reset_index(drop=True)

        if '价格' in df.columns:
            df['close'] = df['价格']

        for col in ['open', 'high', 'low', 'close', 'vol']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        df = df.dropna(subset=['close'])

        print(f"数据加载成功，共{len(df)}条记录")

        # 创建优化器
        optimizer = StrategyOptimizer(df)

        # 运行优化
        best_params, best_return = optimizer.optimize_parameters(max_iterations=30)

        # 分析结果
        optimizer.analyze_optimization_results()

        # 使用最佳参数运行最终测试
        print(f"\n=== 使用最佳参数的最终测试 ===")
        strategy = ChanlunAdvancedStrategy()
        for param, value in best_params.items():
            strategy.params[param] = value

        final_results = strategy.run_strategy(df)

        print(f"最终年化收益率: {final_results['annual_return']:.2%}")
        print(f"总交易次数: {final_results['total_trades']}")
        print(f"胜率: {final_results['win_rate']:.2%}")

        return final_results, best_params

    except Exception as e:
        print(f"优化失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    print("=== 缠论高收益策略测试与优化 ===")

    # 选择运行模式
    mode = input("选择运行模式 (1: 基础测试, 2: 参数优化): ").strip()

    if mode == "2":
        # 运行参数优化
        final_results, best_params = run_strategy_optimization()

        if final_results and final_results['annual_return'] > 0.02:
            print(f"\n🎉 优化成功！年化收益率达到: {final_results['annual_return']:.2%}")
        elif final_results:
            print(f"\n⚠️  需要进一步优化，当前年化收益率: {final_results['annual_return']:.2%}")
    else:
        # 基础测试
        results = load_and_test_strategy()

        if results and results['annual_return'] > 0.02:
            print(f"\n✅ 策略达到目标！年化收益率: {results['annual_return']:.2%}")
        elif results:
            print(f"\n⚠️  策略需要优化，当前年化收益率: {results['annual_return']:.2%}")
        else:
            print(f"\n❌ 策略测试失败")
