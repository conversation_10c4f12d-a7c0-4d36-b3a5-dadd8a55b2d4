#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多股票监控管理器
支持最多200个股票的实时监控、策略分析和自动交易
"""

import threading
import time
import queue
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

# 导入策略相关模块
from 回测系统 import MACDStrategy, KDJStrategy, CustomStrategy

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StockMonitorInfo:
    """单个股票的监控信息"""
    
    def __init__(self, ts_code: str, strategy_type: str = "MACD", strategy_params: Dict = None):
        self.ts_code = ts_code
        self.strategy_type = strategy_type
        self.strategy_params = strategy_params or {}
        
        # 数据状态
        self.current_data = None
        self.last_update_time = None
        self.data_history = []  # 保存最近的历史数据用于策略计算
        
        # 策略状态
        self.strategy = None
        self.last_signal = 0  # 1: 买入, -1: 卖出, 0: 无信号
        self.last_signal_time = None
        self.signal_price = 0.0
        
        # 交易状态
        self.position = 0  # 当前持仓数量
        self.avg_cost = 0.0  # 平均成本
        self.last_trade_time = None
        
        # 监控状态
        self.is_active = True
        self.error_count = 0
        self.last_error = None
        
        self._init_strategy()
    
    def _init_strategy(self):
        """初始化策略"""
        try:
            if self.strategy_type == "MACD":
                self.strategy = MACDStrategy()
                params = {
                    'fast_period': self.strategy_params.get('fast_period', 12),
                    'slow_period': self.strategy_params.get('slow_period', 26),
                    'signal_period': self.strategy_params.get('signal_period', 9)
                }
                self.strategy.set_parameters(**params)
            elif self.strategy_type == "KDJ":
                self.strategy = KDJStrategy()
                params = {
                    'k_period': self.strategy_params.get('k_period', 9),
                    'oversold': self.strategy_params.get('oversold', 20),
                    'overbought': self.strategy_params.get('overbought', 80)
                }
                self.strategy.set_parameters(**params)
            elif self.strategy_type == "CUSTOM":
                strategy_code = self.strategy_params.get('code', '')
                if strategy_code:
                    self.strategy = CustomStrategy("自定义策略", strategy_code)
                else:
                    raise ValueError("自定义策略代码不能为空")
            else:
                raise ValueError(f"不支持的策略类型: {self.strategy_type}")
                
            logger.info(f"股票 {self.ts_code} 策略 {self.strategy_type} 初始化成功")
            
        except Exception as e:
            logger.error(f"股票 {self.ts_code} 策略初始化失败: {e}")
            self.strategy = None
    
    def update_data(self, new_data: pd.Series):
        """更新股票数据"""
        self.current_data = new_data
        self.last_update_time = datetime.now()
        
        # 添加到历史数据（保留最近100条用于策略计算）
        self.data_history.append(new_data)
        if len(self.data_history) > 100:
            self.data_history.pop(0)
        
        # 重置错误计数
        self.error_count = 0
        self.last_error = None
    
    def calculate_signal(self) -> int:
        """计算策略信号"""
        if not self.strategy or len(self.data_history) < 30:  # 至少需要30条数据
            return 0
        
        try:
            # 将历史数据转换为DataFrame
            df = pd.DataFrame(self.data_history)
            if df.empty:
                return 0
            
            # 确保数据格式正确
            required_columns = ['open', 'high', 'low', 'close', 'vol']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"股票 {self.ts_code} 缺少必要列: {col}")
                    return 0
            
            # 运行策略
            signal = self.strategy.generate_signal(df)
            
            # 只有信号发生变化时才更新
            if signal != self.last_signal and signal != 0:
                self.last_signal = signal
                self.last_signal_time = datetime.now()
                self.signal_price = float(self.current_data['close'])
                logger.info(f"股票 {self.ts_code} 生成新信号: {signal} @ {self.signal_price}")
            
            return signal
            
        except Exception as e:
            logger.error(f"股票 {self.ts_code} 策略计算失败: {e}")
            self.error_count += 1
            self.last_error = str(e)
            return 0
    
    def get_status_info(self) -> Dict:
        """获取监控状态信息"""
        return {
            'ts_code': self.ts_code,
            'strategy_type': self.strategy_type,
            'is_active': self.is_active,
            'current_price': float(self.current_data['close']) if self.current_data is not None else 0.0,
            'last_update_time': self.last_update_time,
            'last_signal': self.last_signal,
            'last_signal_time': self.last_signal_time,
            'signal_price': self.signal_price,
            'position': self.position,
            'avg_cost': self.avg_cost,
            'error_count': self.error_count,
            'last_error': self.last_error
        }


class APIRateLimiter:
    """API调用频率限制器"""

    def __init__(self, max_calls_per_minute: int = 60):
        self.max_calls_per_minute = max_calls_per_minute
        self.call_times = []
        self.lock = threading.Lock()

    def wait_if_needed(self):
        """如果需要，等待以避免超过频率限制"""
        with self.lock:
            now = time.time()

            # 清理一分钟前的记录
            self.call_times = [t for t in self.call_times if now - t < 60]

            # 检查是否超过限制
            if len(self.call_times) >= self.max_calls_per_minute:
                # 计算需要等待的时间
                oldest_call = min(self.call_times)
                wait_time = 60 - (now - oldest_call) + 0.1  # 多等0.1秒确保安全

                if wait_time > 0:
                    logger.debug(f"API频率限制，等待 {wait_time:.1f} 秒")
                    time.sleep(wait_time)

            # 记录本次调用
            self.call_times.append(time.time())


class MultiStockMonitor:
    """多股票监控管理器"""

    def __init__(self, market_data_manager, max_stocks: int = 200):
        self.market_data_manager = market_data_manager
        self.max_stocks = max_stocks

        # 监控股票字典
        self.monitored_stocks: Dict[str, StockMonitorInfo] = {}

        # 控制变量
        self.is_running = False
        self.monitor_thread = None
        self.update_interval = 30  # 默认30秒更新一次

        # 线程池用于并发处理
        self.thread_pool = ThreadPoolExecutor(max_workers=10)

        # 信号队列
        self.signal_queue = queue.Queue()

        # API频率限制器
        self.rate_limiter = APIRateLimiter(max_calls_per_minute=60)

        # 性能监控
        self.performance_stats = {
            'total_api_calls': 0,
            'failed_api_calls': 0,
            'avg_response_time': 0.0,
            'last_cleanup_time': datetime.now()
        }

        # 回调函数
        self.data_update_callback = None
        self.signal_callback = None
        self.error_callback = None

        logger.info(f"多股票监控管理器初始化完成，最大监控数量: {max_stocks}")
    
    def add_stock(self, ts_code: str, strategy_type: str = "MACD", 
                  strategy_params: Dict = None) -> bool:
        """添加股票到监控列表"""
        if len(self.monitored_stocks) >= self.max_stocks:
            logger.warning(f"已达到最大监控数量限制: {self.max_stocks}")
            return False
        
        if ts_code in self.monitored_stocks:
            logger.warning(f"股票 {ts_code} 已在监控列表中")
            return False
        
        try:
            stock_info = StockMonitorInfo(ts_code, strategy_type, strategy_params)
            self.monitored_stocks[ts_code] = stock_info
            logger.info(f"股票 {ts_code} 已添加到监控列表，策略: {strategy_type}")
            return True
        except Exception as e:
            logger.error(f"添加股票 {ts_code} 失败: {e}")
            return False
    
    def remove_stock(self, ts_code: str) -> bool:
        """从监控列表中移除股票"""
        if ts_code in self.monitored_stocks:
            del self.monitored_stocks[ts_code]
            logger.info(f"股票 {ts_code} 已从监控列表中移除")
            return True
        return False
    
    def get_monitored_stocks(self) -> List[str]:
        """获取当前监控的股票列表"""
        return list(self.monitored_stocks.keys())
    
    def set_callbacks(self, data_update_callback: Callable = None,
                     signal_callback: Callable = None,
                     error_callback: Callable = None):
        """设置回调函数"""
        self.data_update_callback = data_update_callback
        self.signal_callback = signal_callback
        self.error_callback = error_callback
    
    def start_monitoring(self, update_interval: int = 30):
        """开始监控"""
        if self.is_running:
            logger.warning("监控已在运行中")
            return
        
        if not self.monitored_stocks:
            logger.warning("没有股票需要监控")
            return
        
        self.update_interval = update_interval
        self.is_running = True
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info(f"开始监控 {len(self.monitored_stocks)} 只股票，更新间隔: {update_interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("多股票监控已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        loop_count = 0

        while self.is_running:
            try:
                start_time = time.time()
                loop_count += 1

                # 批量获取实时数据
                self._update_all_stocks_data()

                # 并发计算策略信号
                self._calculate_all_signals()

                # 定期执行清理和优化（每10个循环执行一次）
                if loop_count % 10 == 0:
                    self._periodic_maintenance()

                # 计算实际耗时
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.update_interval - elapsed_time)

                logger.debug(f"监控循环完成，耗时: {elapsed_time:.2f}秒，等待: {sleep_time:.2f}秒")

                # 等待下一次更新
                time.sleep(sleep_time)

            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                if self.error_callback:
                    self.error_callback(f"监控循环出错: {e}")
                time.sleep(10)  # 出错时等待10秒再重试

    def _periodic_maintenance(self):
        """定期维护任务"""
        try:
            # 清理过期数据
            if (datetime.now() - self.performance_stats['last_cleanup_time']).total_seconds() > 300:  # 5分钟
                cleaned_count = self.cleanup_old_data()
                self.performance_stats['last_cleanup_time'] = datetime.now()

                if cleaned_count > 0:
                    logger.info(f"定期清理完成，清理了 {cleaned_count} 条数据")

            # 性能优化
            self.optimize_performance()

            # 记录性能指标
            metrics = self.get_performance_metrics()
            logger.debug(f"性能指标: API成功率={1-metrics['error_rate']:.2%}, "
                        f"更新成功率={metrics['update_success_rate']:.2%}, "
                        f"内存使用={metrics['memory_usage_mb']:.1f}MB")

        except Exception as e:
            logger.error(f"定期维护任务失败: {e}")
    
    def _update_all_stocks_data(self):
        """批量更新所有股票的实时数据"""
        stock_codes = list(self.monitored_stocks.keys())
        if not stock_codes:
            return

        try:
            # 优化：使用批量API获取实时数据
            start_time = time.time()

            # 分批获取数据（避免单次请求过多，优化API调用）
            batch_size = 30  # 减小批次大小，提高响应速度
            successful_updates = 0
            failed_updates = 0

            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]

                # 尝试批量获取（如果支持）
                try:
                    # 检查是否支持批量获取
                    if hasattr(self.market_data_manager, 'get_realtime_data_batch'):
                        batch_data = self.market_data_manager.get_realtime_data_batch(batch_codes)

                        for ts_code in batch_codes:
                            if ts_code in batch_data and not batch_data[ts_code].empty:
                                data_series = batch_data[ts_code].iloc[0]
                                self.monitored_stocks[ts_code].update_data(data_series)
                                successful_updates += 1
                            else:
                                logger.warning(f"股票 {ts_code} 批量获取数据为空")
                                failed_updates += 1
                    else:
                        # 逐个获取（兼容模式）
                        for ts_code in batch_codes:
                            try:
                                # 应用频率限制
                                self.rate_limiter.wait_if_needed()

                                api_start_time = time.time()
                                realtime_data = self.market_data_manager.get_realtime_data(ts_code, use_cache=False)
                                api_time = time.time() - api_start_time

                                # 更新性能统计
                                self.performance_stats['total_api_calls'] += 1
                                self.performance_stats['avg_response_time'] = (
                                    self.performance_stats['avg_response_time'] * 0.9 + api_time * 0.1
                                )

                                if not realtime_data.empty:
                                    data_series = realtime_data.iloc[0]
                                    self.monitored_stocks[ts_code].update_data(data_series)
                                    successful_updates += 1
                                else:
                                    logger.warning(f"股票 {ts_code} 实时数据为空")
                                    failed_updates += 1

                            except Exception as e:
                                logger.error(f"获取股票 {ts_code} 实时数据失败: {e}")
                                self.monitored_stocks[ts_code].error_count += 1
                                self.monitored_stocks[ts_code].last_error = str(e)
                                self.performance_stats['failed_api_calls'] += 1
                                failed_updates += 1

                except Exception as e:
                    logger.error(f"批量获取数据失败: {e}")
                    failed_updates += len(batch_codes)

                # 动态调整批次间延迟（根据API响应时间）
                batch_time = time.time() - start_time
                if batch_time < 1.0:  # 如果响应很快，减少延迟
                    time.sleep(0.2)
                else:  # 如果响应较慢，增加延迟
                    time.sleep(0.8)

            # 记录性能统计
            total_time = time.time() - start_time
            logger.debug(f"数据更新完成: 成功{successful_updates}, 失败{failed_updates}, 耗时{total_time:.2f}秒")

            # 调用数据更新回调
            if self.data_update_callback:
                self.data_update_callback(self.get_all_status())

        except Exception as e:
            logger.error(f"批量更新股票数据失败: {e}")
    
    def _calculate_all_signals(self):
        """并发计算所有股票的策略信号"""
        if not self.monitored_stocks:
            return
        
        # 提交所有策略计算任务
        future_to_stock = {}
        for ts_code, stock_info in self.monitored_stocks.items():
            if stock_info.is_active:
                future = self.thread_pool.submit(stock_info.calculate_signal)
                future_to_stock[future] = ts_code
        
        # 收集结果
        new_signals = []
        for future in as_completed(future_to_stock):
            ts_code = future_to_stock[future]
            try:
                signal = future.result()
                if signal != 0:  # 有新信号
                    stock_info = self.monitored_stocks[ts_code]
                    signal_data = {
                        'ts_code': ts_code,
                        'signal': signal,
                        'price': stock_info.signal_price,
                        'time': stock_info.last_signal_time,
                        'strategy_type': stock_info.strategy_type
                    }
                    new_signals.append(signal_data)
                    
                    # 添加到信号队列
                    self.signal_queue.put(signal_data)
                    
            except Exception as e:
                logger.error(f"股票 {ts_code} 策略计算异常: {e}")
        
        # 调用信号回调
        if new_signals and self.signal_callback:
            self.signal_callback(new_signals)
    
    def get_all_status(self) -> Dict[str, Dict]:
        """获取所有股票的监控状态"""
        return {ts_code: stock_info.get_status_info() 
                for ts_code, stock_info in self.monitored_stocks.items()}
    
    def get_pending_signals(self) -> List[Dict]:
        """获取待处理的信号"""
        signals = []
        while not self.signal_queue.empty():
            try:
                signals.append(self.signal_queue.get_nowait())
            except queue.Empty:
                break
        return signals
    
    def update_position(self, ts_code: str, position: int, avg_cost: float = 0.0):
        """更新股票持仓信息"""
        if ts_code in self.monitored_stocks:
            self.monitored_stocks[ts_code].position = position
            self.monitored_stocks[ts_code].avg_cost = avg_cost
            self.monitored_stocks[ts_code].last_trade_time = datetime.now()
    
    def set_stock_active(self, ts_code: str, active: bool):
        """设置股票监控状态"""
        if ts_code in self.monitored_stocks:
            self.monitored_stocks[ts_code].is_active = active
    
    def get_statistics(self) -> Dict:
        """获取监控统计信息"""
        total_stocks = len(self.monitored_stocks)
        active_stocks = sum(1 for stock in self.monitored_stocks.values() if stock.is_active)
        error_stocks = sum(1 for stock in self.monitored_stocks.values() if stock.error_count > 0)

        # 计算内存使用情况
        total_data_points = sum(len(stock.data_history) for stock in self.monitored_stocks.values())

        return {
            'total_stocks': total_stocks,
            'active_stocks': active_stocks,
            'error_stocks': error_stocks,
            'is_running': self.is_running,
            'update_interval': self.update_interval,
            'total_data_points': total_data_points,
            'memory_usage_mb': self._estimate_memory_usage()
        }

    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        try:
            import sys
            total_size = 0

            # 估算每个股票监控信息的内存使用
            for stock_info in self.monitored_stocks.values():
                # 历史数据大小
                if stock_info.data_history:
                    total_size += sys.getsizeof(stock_info.data_history)
                    for data_point in stock_info.data_history:
                        total_size += sys.getsizeof(data_point)

                # 其他属性大小
                total_size += sys.getsizeof(stock_info.ts_code)
                total_size += sys.getsizeof(stock_info.strategy_type)
                total_size += sys.getsizeof(stock_info.strategy_params)

            return total_size / (1024 * 1024)  # 转换为MB
        except:
            return 0.0

    def cleanup_old_data(self, max_history_days: int = 7):
        """清理过期的历史数据"""
        cutoff_time = datetime.now() - timedelta(days=max_history_days)
        cleaned_count = 0

        for stock_info in self.monitored_stocks.values():
            if stock_info.data_history:
                # 保留最近的数据
                original_count = len(stock_info.data_history)
                stock_info.data_history = stock_info.data_history[-50:]  # 只保留最近50条
                cleaned_count += original_count - len(stock_info.data_history)

        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 条过期数据")

        return cleaned_count

    def optimize_performance(self):
        """性能优化"""
        # 清理过期数据
        self.cleanup_old_data()

        # 调整线程池大小
        active_stocks = sum(1 for stock in self.monitored_stocks.values() if stock.is_active)
        optimal_workers = min(max(2, active_stocks // 10), 20)  # 动态调整工作线程数

        if hasattr(self.thread_pool, '_max_workers'):
            current_workers = self.thread_pool._max_workers
            if current_workers != optimal_workers:
                logger.info(f"调整线程池大小: {current_workers} -> {optimal_workers}")
                # 重新创建线程池
                self.thread_pool.shutdown(wait=False)
                self.thread_pool = ThreadPoolExecutor(max_workers=optimal_workers)

        # 动态调整更新间隔
        if active_stocks > 100:
            recommended_interval = max(30, self.update_interval)
        elif active_stocks > 50:
            recommended_interval = max(20, self.update_interval)
        else:
            recommended_interval = max(10, self.update_interval)

        if recommended_interval != self.update_interval:
            logger.info(f"建议调整更新间隔: {self.update_interval}s -> {recommended_interval}s")

    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        stats = self.get_statistics()

        # 计算平均错误率
        total_errors = sum(stock.error_count for stock in self.monitored_stocks.values())
        error_rate = total_errors / max(1, len(self.monitored_stocks))

        # 计算数据更新成功率
        recent_updates = sum(1 for stock in self.monitored_stocks.values()
                           if stock.last_update_time and
                           (datetime.now() - stock.last_update_time).total_seconds() < self.update_interval * 2)
        update_success_rate = recent_updates / max(1, len(self.monitored_stocks))

        return {
            **stats,
            'error_rate': error_rate,
            'update_success_rate': update_success_rate,
            'thread_pool_size': getattr(self.thread_pool, '_max_workers', 0),
            'signal_queue_size': self.signal_queue.qsize()
        }
