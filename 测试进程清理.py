#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进程清理功能
验证股票软件关闭时是否能正确清理所有后台进程
"""

import os
import sys
import time
import subprocess
import psutil
from datetime import datetime

def get_python_processes():
    """获取所有Python进程"""
    python_processes = []
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and len(cmdline) > 1:
                        script_name = os.path.basename(cmdline[1]) if len(cmdline) > 1 else ""
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'script': script_name,
                            'cmdline': ' '.join(cmdline) if cmdline else ""
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except Exception as e:
        print(f"获取进程列表失败: {e}")
    
    return python_processes

def filter_related_processes(processes):
    """过滤出相关的进程"""
    related_keywords = ['登录注册', '股票看图软件', 'tushare', 'stock']
    related_processes = []
    
    for proc in processes:
        script = proc['script'].lower()
        cmdline = proc['cmdline'].lower()
        
        if any(keyword in script or keyword in cmdline for keyword in related_keywords):
            related_processes.append(proc)
    
    return related_processes

def print_processes(processes, title):
    """打印进程列表"""
    print(f"\n{title}")
    print("=" * 80)
    if not processes:
        print("未找到相关进程")
    else:
        for proc in processes:
            print(f"PID: {proc['pid']:6} | 脚本: {proc['script']:20} | 命令: {proc['cmdline'][:50]}...")
    print("=" * 80)

def test_process_cleanup():
    """测试进程清理功能"""
    print("进程清理测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 获取测试前的进程状态
    print("\n1. 获取测试前的进程状态...")
    initial_processes = get_python_processes()
    initial_related = filter_related_processes(initial_processes)
    print_processes(initial_related, "测试前相关进程")
    
    # 2. 启动登录程序
    print("\n2. 启动登录程序...")
    try:
        login_script = os.path.join(os.path.dirname(__file__), "登录注册.py")
        if not os.path.exists(login_script):
            print("错误: 未找到登录注册.py文件")
            return
        
        print("正在启动登录程序...")
        # 注意：这里不等待进程完成，因为登录程序会启动GUI
        login_process = subprocess.Popen([sys.executable, login_script],
                                       cwd=os.path.dirname(__file__))
        
        print(f"登录程序已启动，PID: {login_process.pid}")
        
        # 等待一段时间让程序完全启动
        print("等待程序启动...")
        time.sleep(10)
        
        # 3. 检查启动后的进程状态
        print("\n3. 检查启动后的进程状态...")
        after_start_processes = get_python_processes()
        after_start_related = filter_related_processes(after_start_processes)
        print_processes(after_start_related, "启动后相关进程")
        
        # 4. 等待用户手动测试
        print("\n4. 手动测试阶段")
        print("请按照以下步骤进行测试:")
        print("1) 在登录界面中完成登录流程")
        print("2) 等待股票看图软件启动")
        print("3) 关闭股票看图软件窗口（点击X按钮）")
        print("4) 观察是否还有后台进程残留")
        print("\n按回车键继续监控进程状态...")
        input()
        
        # 5. 持续监控进程状态
        print("\n5. 开始监控进程状态...")
        monitor_count = 0
        max_monitor = 30  # 最多监控30次，每次间隔2秒
        
        while monitor_count < max_monitor:
            current_processes = get_python_processes()
            current_related = filter_related_processes(current_processes)
            
            print(f"\n监控第 {monitor_count + 1} 次 ({datetime.now().strftime('%H:%M:%S')})")
            print_processes(current_related, f"当前相关进程 (共{len(current_related)}个)")
            
            # 如果没有相关进程了，说明清理成功
            if not current_related:
                print("\n✅ 进程清理测试通过！所有相关进程已正确清理。")
                break
            
            monitor_count += 1
            time.sleep(2)
        
        if monitor_count >= max_monitor:
            print(f"\n⚠️ 监控超时，仍有 {len(current_related)} 个相关进程在运行")
            print("可能存在进程清理问题，请手动检查")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
    
    # 6. 最终清理
    print("\n6. 最终清理...")
    try:
        # 强制清理所有相关进程
        final_processes = get_python_processes()
        final_related = filter_related_processes(final_processes)
        
        if final_related:
            print("发现残留进程，正在强制清理...")
            for proc in final_related:
                try:
                    pid = proc['pid']
                    if pid != os.getpid():  # 不要杀死自己
                        print(f"强制终止进程 PID: {pid}")
                        if os.name == 'nt':  # Windows
                            subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                         capture_output=True, check=False)
                        else:  # Unix/Linux
                            os.kill(pid, 9)
                except Exception as e:
                    print(f"终止进程 {pid} 失败: {e}")
        
        # 清理临时文件
        temp_files = ["login_process.pid", "tushare_token.txt"]
        for temp_file in temp_files:
            temp_path = os.path.join(os.path.dirname(__file__), temp_file)
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    print(f"已删除临时文件: {temp_file}")
                except Exception as e:
                    print(f"删除临时文件 {temp_file} 失败: {e}")
        
    except Exception as e:
        print(f"最终清理失败: {e}")
    
    print(f"\n进程清理测试结束 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")

def show_current_processes():
    """显示当前相关进程"""
    print("当前Python相关进程:")
    processes = get_python_processes()
    related = filter_related_processes(processes)
    print_processes(related, "相关进程")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--show":
        show_current_processes()
    else:
        test_process_cleanup()
