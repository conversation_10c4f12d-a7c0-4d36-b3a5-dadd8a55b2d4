# 多股票监控系统使用说明

## 概述

多股票监控系统是对原有股票分析软件的重大升级，支持同时监控最多200只股票的实时数据，并根据策略自动生成买卖信号，实现自动交易功能。

## 主要功能

### 1. 多股票实时监控
- 支持同时监控最多200只股票
- 实时获取股票价格数据
- 支持MACD、KDJ和自定义策略
- 智能API调用频率控制
- 内存使用优化

### 2. 智能交易调度
- 自动处理买卖信号队列
- 风险控制机制
- 防止同时执行过多交易
- 交易失败自动重试

### 3. 风险管理
- 单股票最大持仓限制
- 总持仓股票数量控制
- 每日交易次数限制
- 资金分配管理

## 使用步骤

### 第一步：启动程序
1. 运行 `登录注册.py`
2. 登录成功后会自动启动股票看图软件
3. 点击"网页交易"选项卡
4. 选择"多股票监控"子选项卡

### 第二步：添加监控股票

#### 方法一：手动添加
1. 在"股票选择"区域选择"手动添加"
2. 输入股票代码（如：000001.SZ）
3. 点击"添加到监控"按钮

#### 方法二：从股票池添加
1. 选择"从股票池选择"
2. 选择股票池（沪深300、中证1000等）
3. 点击"从股票池添加"按钮
4. 系统会自动添加该股票池的股票（最多50只）

#### 方法三：从选股结果添加
1. 先在其他功能中进行选股
2. 选择"从选股结果"
3. 系统会导入选股结果

### 第三步：配置策略
1. 在"策略配置"区域选择默认策略：
   - **MACD策略**：适合趋势跟踪
   - **KDJ策略**：适合短线操作
   - **自定义策略**：使用自己编写的策略代码

2. 策略参数会自动应用到新添加的股票

### 第四步：启动监控
1. 设置更新间隔（建议30秒以上）
2. 点击"开始监控"按钮
3. 系统开始实时获取数据并计算策略信号

### 第五步：配置自动交易（可选）
1. 确保已连接交易网站
2. 设置风险控制参数：
   - 单股票最大金额
   - 最大持仓股票数
3. 勾选"启用自动交易"
4. 系统会自动执行买卖信号

## 界面说明

### 左侧控制面板
- **股票选择**：添加要监控的股票
- **策略配置**：设置默认策略类型
- **监控控制**：启动/停止监控
- **交易控制**：自动交易设置

### 中间监控列表
显示所有监控股票的实时状态：
- 股票代码
- 当前价格
- 使用策略
- 最新信号（买入/卖出）
- 信号时间
- 当前持仓
- 监控状态

### 右侧状态面板
- **监控状态**：显示系统运行状态
- **最新信号**：实时显示买卖信号
- **交易日志**：记录所有交易活动

## 风险控制参数

### 系统默认设置
- 单股票最大持仓：10,000股
- 最大持仓股票数：50只
- 每日最大交易次数：100次
- 单笔最大交易金额：100,000元
- 同一股票最小交易间隔：60秒

### 自定义设置
可以在交易控制面板中修改：
- 单股票最大金额
- 最大持仓股票数

## 性能优化

### 自动优化功能
- API调用频率控制（默认60次/分钟）
- 内存使用监控和清理
- 线程池大小动态调整
- 历史数据自动清理

### 建议设置
- **监控股票数 < 50**：更新间隔可设为20-30秒
- **监控股票数 50-100**：更新间隔建议30-60秒
- **监控股票数 > 100**：更新间隔建议60秒以上

## 常见问题

### Q1：为什么有些股票显示"错误"状态？
A1：可能原因：
- 股票代码错误
- 网络连接问题
- API调用频率过高
- 股票已停牌

解决方法：
- 检查股票代码格式
- 检查网络连接
- 增加更新间隔
- 移除停牌股票

### Q2：自动交易为什么没有执行？
A2：可能原因：
- 未连接交易网站
- 风险控制限制
- 资金不足
- 持仓已满

解决方法：
- 确保交易网站连接正常
- 检查风险控制设置
- 确认账户资金充足
- 调整持仓限制

### Q3：系统运行缓慢怎么办？
A3：优化建议：
- 减少监控股票数量
- 增加更新间隔
- 定期重启系统
- 查看性能报告

### Q4：如何查看详细的性能信息？
A4：点击右侧状态面板的"性能报告"按钮，可以查看：
- API调用统计
- 内存使用情况
- 系统性能指标
- 优化建议

## 注意事项

### 重要提醒
1. **风险警告**：自动交易存在风险，请谨慎使用
2. **资金安全**：建议先用小额资金测试
3. **策略验证**：建议先进行回测验证策略有效性
4. **监控限制**：不要超过200只股票的监控限制
5. **网络稳定**：确保网络连接稳定

### 最佳实践
1. 从少量股票开始，逐步增加
2. 定期查看性能报告，优化设置
3. 根据市场情况调整策略参数
4. 保持合理的风险控制设置
5. 定期备份重要配置

## 技术支持

如遇到问题，请：
1. 查看交易日志中的错误信息
2. 检查性能报告中的建议
3. 确认网络和数据源连接正常
4. 重启系统尝试解决

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多股票监控
- 实现自动交易功能
- 添加风险控制机制
- 性能优化和资源管理
