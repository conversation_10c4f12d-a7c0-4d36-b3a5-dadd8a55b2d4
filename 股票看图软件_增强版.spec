# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['股票看图软件_增强版.py'],
    pathex=[],
    binaries=[],
    datas=[('回测系统.py', '.'), ('回测分析.py', '.'), ('策略模板.py', '.'), ('多股票回测系统.py', '.'), ('技术指标库.py', '.'), ('策略示例', '策略示例')],
    hiddenimports=['tkinter', 'tushare', 'pandas', 'matplotlib', 'selenium'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['PyQt5', 'PySide6', 'PyQt6', 'PySide2'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='股票看图软件_增强版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
