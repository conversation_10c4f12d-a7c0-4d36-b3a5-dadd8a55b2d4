# 进程清理修复说明

## 问题描述

之前的版本中，当用户关闭股票看图软件后，登录注册程序仍会在后台运行，导致系统中存在残留进程。

## 修复内容

### 1. 登录注册.py 修改

- **添加进程标识文件**：登录成功启动股票软件时，创建 `login_process.pid` 文件记录登录程序的进程ID
- **添加进程监控**：启动后台线程监控股票软件进程状态
- **自动清理机制**：当检测到股票软件关闭时，自动清理登录程序和相关资源

### 2. 股票看图软件_增强版.py 修改

- **增强关闭处理**：在 `on_closing()` 方法中添加完整的资源清理
- **登录进程清理**：主动查找并终止登录程序进程
- **强制退出机制**：确保所有相关进程都能正确退出

## 修复机制

### 进程监控流程

```
登录程序启动 → 创建PID文件 → 启动股票软件 → 启动监控线程
                                        ↓
股票软件关闭 ← 清理登录进程 ← 检测到股票软件退出 ← 监控线程
```

### 双重保险机制

1. **主动监控**：登录程序监控股票软件进程，自动退出
2. **被动清理**：股票软件关闭时主动清理登录程序

## 测试方法

### 方法一：使用测试脚本

1. **检查当前进程状态**：
   ```bash
   python 快速测试清理.py
   ```

2. **完整测试流程**：
   - 运行 `登录注册.py`
   - 完成登录，等待股票软件启动
   - 关闭股票软件窗口
   - 再次运行 `python 快速测试清理.py` 检查是否有残留进程

3. **手动清理（如果需要）**：
   ```bash
   python 快速测试清理.py --clean
   ```

### 方法二：使用任务管理器

1. **Windows系统**：
   - 打开任务管理器（Ctrl+Shift+Esc）
   - 查看"进程"选项卡
   - 查找包含"python"的进程
   - 检查是否有与登录或股票软件相关的进程

2. **测试步骤**：
   - 记录测试前的Python进程数量
   - 运行登录程序并启动股票软件
   - 关闭股票软件
   - 检查Python进程数量是否恢复到测试前状态

## 预期效果

### 修复前
- 关闭股票软件后，登录程序继续在后台运行
- 任务管理器中可以看到残留的Python进程
- 需要手动结束进程

### 修复后
- 关闭股票软件后，登录程序自动退出
- 所有相关进程都被正确清理
- 任务管理器中不会有残留进程

## 技术细节

### 新增文件

- `login_process.pid`：临时文件，记录登录程序进程ID
- `tushare_token.txt`：临时文件，传递Token信息（已存在）

### 关键代码修改

1. **进程监控线程**：
   ```python
   def start_process_monitor(self, process):
       def monitor_process():
           process.wait()  # 等待股票软件进程结束
           self.cleanup_and_exit()  # 清理并退出
   ```

2. **资源清理**：
   ```python
   def cleanup_and_exit(self):
       # 删除PID文件
       # 停止定时器
       # 关闭浏览器
       # 强制退出程序
   ```

3. **登录进程清理**：
   ```python
   def cleanup_login_process(self):
       # 读取PID文件
       # 终止登录进程
       # 删除临时文件
   ```

## 故障排除

### 如果仍有残留进程

1. **检查错误日志**：查看控制台输出的错误信息
2. **手动清理**：运行 `python 快速测试清理.py --clean`
3. **重启系统**：作为最后手段

### 常见问题

1. **Q: 股票软件关闭很慢**
   - A: 这是正常的，程序需要时间清理所有资源

2. **Q: 有时还是会有残留进程**
   - A: 可能是程序异常退出，使用测试脚本手动清理

3. **Q: 登录程序没有自动退出**
   - A: 检查是否有错误提示，可能是权限问题

## 验证成功标志

✅ 关闭股票软件后，任务管理器中没有相关的Python进程
✅ 运行测试脚本显示"没有发现相关的后台进程"
✅ 临时文件（login_process.pid）被正确删除
✅ 系统资源占用恢复正常

## 注意事项

1. **正常关闭**：请使用窗口的关闭按钮（X）正常关闭程序
2. **避免强制结束**：不要使用任务管理器强制结束进程，这可能导致清理机制无法正常工作
3. **等待清理**：关闭程序后请等待几秒钟，让清理机制完成工作

## 更新日志

- **v1.1.0**：添加进程监控和自动清理机制
- **v1.1.1**：增强错误处理和资源清理
- **v1.1.2**：添加双重保险机制和测试工具
