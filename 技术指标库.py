#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
技术指标函数库
提供常用的技术指标计算函数，供用户编写自定义策略使用
"""

import pandas as pd
import numpy as np
from typing import Union, Tuple

class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def SMA(data: pd.Series, period: int) -> pd.Series:
        """
        简单移动平均线 (Simple Moving Average)
        
        Args:
            data: 价格序列
            period: 计算周期
            
        Returns:
            SMA序列
        """
        return data.rolling(window=period).mean()
    
    @staticmethod
    def EMA(data: pd.Series, period: int) -> pd.Series:
        """
        指数移动平均线 (Exponential Moving Average)
        
        Args:
            data: 价格序列
            period: 计算周期
            
        Returns:
            EMA序列
        """
        return data.ewm(span=period, adjust=False).mean()
    
    @staticmethod
    def RSI(data: pd.Series, period: int = 14) -> pd.Series:
        """
        相对强弱指标 (Relative Strength Index)
        
        Args:
            data: 价格序列
            period: 计算周期，默认14
            
        Returns:
            RSI序列 (0-100)
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def MACD(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD指标 (Moving Average Convergence Divergence)
        
        Args:
            data: 价格序列
            fast: 快线周期，默认12
            slow: 慢线周期，默认26
            signal: 信号线周期，默认9
            
        Returns:
            (DIF, DEA, MACD)元组
        """
        ema_fast = TechnicalIndicators.EMA(data, fast)
        ema_slow = TechnicalIndicators.EMA(data, slow)
        dif = ema_fast - ema_slow
        dea = TechnicalIndicators.EMA(dif, signal)
        macd = 2 * (dif - dea)
        return dif, dea, macd
    
    @staticmethod
    def KDJ(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        KDJ指标
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期，默认9
            
        Returns:
            (K, D, J)元组
        """
        low_min = low.rolling(window=period).min()
        high_max = high.rolling(window=period).max()
        rsv = (close - low_min) / (high_max - low_min) * 100
        k = rsv.ewm(com=2).mean()
        d = k.ewm(com=2).mean()
        j = 3 * k - 2 * d
        return k, d, j
    
    @staticmethod
    def BOLL(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        布林带 (Bollinger Bands)
        
        Args:
            data: 价格序列
            period: 计算周期，默认20
            std_dev: 标准差倍数，默认2
            
        Returns:
            (上轨, 中轨, 下轨)元组
        """
        middle = TechnicalIndicators.SMA(data, period)
        std = data.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        return upper, middle, lower
    
    @staticmethod
    def ATR(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        平均真实波幅 (Average True Range)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期，默认14
            
        Returns:
            ATR序列
        """
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean()
        return atr
    
    @staticmethod
    def STOCH(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
        """
        随机指标 (Stochastic Oscillator)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            k_period: K值计算周期，默认14
            d_period: D值计算周期，默认3
            
        Returns:
            (K, D)元组
        """
        low_min = low.rolling(window=k_period).min()
        high_max = high.rolling(window=k_period).max()
        k = ((close - low_min) / (high_max - low_min)) * 100
        d = k.rolling(window=d_period).mean()
        return k, d
    
    @staticmethod
    def CCI(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """
        顺势指标 (Commodity Channel Index)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期，默认20
            
        Returns:
            CCI序列
        """
        tp = (high + low + close) / 3
        sma_tp = tp.rolling(window=period).mean()
        mad = tp.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        cci = (tp - sma_tp) / (0.015 * mad)
        return cci
    
    @staticmethod
    def WR(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """
        威廉指标 (Williams %R)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            period: 计算周期，默认14
            
        Returns:
            WR序列 (-100到0)
        """
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()
        wr = ((high_max - close) / (high_max - low_min)) * -100
        return wr
    
    @staticmethod
    def ROC(data: pd.Series, period: int = 12) -> pd.Series:
        """
        变动率指标 (Rate of Change)
        
        Args:
            data: 价格序列
            period: 计算周期，默认12
            
        Returns:
            ROC序列
        """
        roc = ((data - data.shift(period)) / data.shift(period)) * 100
        return roc
    
    @staticmethod
    def MOMENTUM(data: pd.Series, period: int = 10) -> pd.Series:
        """
        动量指标 (Momentum)
        
        Args:
            data: 价格序列
            period: 计算周期，默认10
            
        Returns:
            动量序列
        """
        momentum = data - data.shift(period)
        return momentum
    
    @staticmethod
    def OBV(close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        能量潮指标 (On Balance Volume)
        
        Args:
            close: 收盘价序列
            volume: 成交量序列
            
        Returns:
            OBV序列
        """
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    @staticmethod
    def VWAP(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        成交量加权平均价 (Volume Weighted Average Price)
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            volume: 成交量序列
            
        Returns:
            VWAP序列
        """
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap

# 便捷函数，直接使用函数名调用
def SMA(data: pd.Series, period: int) -> pd.Series:
    """简单移动平均线"""
    return TechnicalIndicators.SMA(data, period)

def EMA(data: pd.Series, period: int) -> pd.Series:
    """指数移动平均线"""
    return TechnicalIndicators.EMA(data, period)

def RSI(data: pd.Series, period: int = 14) -> pd.Series:
    """相对强弱指标"""
    return TechnicalIndicators.RSI(data, period)

def MACD(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """MACD指标"""
    return TechnicalIndicators.MACD(data, fast, slow, signal)

def KDJ(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """KDJ指标"""
    return TechnicalIndicators.KDJ(high, low, close, period)

def BOLL(data: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """布林带"""
    return TechnicalIndicators.BOLL(data, period, std_dev)

def ATR(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """平均真实波幅"""
    return TechnicalIndicators.ATR(high, low, close, period)

def STOCH(high: pd.Series, low: pd.Series, close: pd.Series, k_period: int = 14, d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
    """随机指标"""
    return TechnicalIndicators.STOCH(high, low, close, k_period, d_period)

def CCI(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
    """顺势指标"""
    return TechnicalIndicators.CCI(high, low, close, period)

def WR(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
    """威廉指标"""
    return TechnicalIndicators.WR(high, low, close, period)

def ROC(data: pd.Series, period: int = 12) -> pd.Series:
    """变动率指标"""
    return TechnicalIndicators.ROC(data, period)

def MOMENTUM(data: pd.Series, period: int = 10) -> pd.Series:
    """动量指标"""
    return TechnicalIndicators.MOMENTUM(data, period)

def OBV(close: pd.Series, volume: pd.Series) -> pd.Series:
    """能量潮指标"""
    return TechnicalIndicators.OBV(close, volume)

def VWAP(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
    """成交量加权平均价"""
    return TechnicalIndicators.VWAP(high, low, close, volume)

# 缠论相关函数
class ChanLunIndicators:
    """缠论技术指标类"""

    @staticmethod
    def merge_k_lines(high: pd.Series, low: pd.Series, close: pd.Series, open_price: pd.Series) -> pd.DataFrame:
        """
        K线合并 - 缠论基础
        合并包含关系的K线
        """
        df = pd.DataFrame({
            'high': high,
            'low': low,
            'close': close,
            'open': open_price
        })

        merged_data = []
        i = 0

        while i < len(df):
            current = df.iloc[i].copy()
            j = i + 1

            # 检查包含关系并合并
            while j < len(df):
                next_bar = df.iloc[j]

                # 判断包含关系
                if (current['high'] >= next_bar['high'] and current['low'] <= next_bar['low']) or \
                   (current['high'] <= next_bar['high'] and current['low'] >= next_bar['low']):
                    # 有包含关系，进行合并
                    if i > 0:
                        prev_bar = merged_data[-1] if merged_data else df.iloc[i-1]
                        # 根据前一根K线的方向决定合并方式
                        if prev_bar['high'] > current['high']:  # 下降趋势
                            current['high'] = min(current['high'], next_bar['high'])
                            current['low'] = min(current['low'], next_bar['low'])
                        else:  # 上升趋势
                            current['high'] = max(current['high'], next_bar['high'])
                            current['low'] = max(current['low'], next_bar['low'])
                    else:
                        # 第一根K线，按高低点合并
                        current['high'] = max(current['high'], next_bar['high'])
                        current['low'] = min(current['low'], next_bar['low'])

                    current['close'] = next_bar['close']
                    j += 1
                else:
                    break

            merged_data.append(current)
            i = j

        return pd.DataFrame(merged_data)

    @staticmethod
    def find_fractal_points(high: pd.Series, low: pd.Series) -> Tuple[pd.Series, pd.Series]:
        """
        寻找分型点（顶分型和底分型）
        """
        tops = pd.Series(index=high.index, dtype=float)
        bottoms = pd.Series(index=low.index, dtype=float)

        for i in range(1, len(high) - 1):
            # 顶分型：中间K线高点是三根K线中最高的
            if high.iloc[i] > high.iloc[i-1] and high.iloc[i] > high.iloc[i+1]:
                tops.iloc[i] = high.iloc[i]

            # 底分型：中间K线低点是三根K线中最低的
            if low.iloc[i] < low.iloc[i-1] and low.iloc[i] < low.iloc[i+1]:
                bottoms.iloc[i] = low.iloc[i]

        return tops, bottoms

    @staticmethod
    def find_pen_lines(tops: pd.Series, bottoms: pd.Series) -> pd.DataFrame:
        """
        寻找笔（连接分型点的线段）
        """
        # 合并顶底分型点
        points = []

        for i in range(len(tops)):
            if pd.notna(tops.iloc[i]):
                points.append({'index': i, 'price': tops.iloc[i], 'type': 'top'})
            if pd.notna(bottoms.iloc[i]):
                points.append({'index': i, 'price': bottoms.iloc[i], 'type': 'bottom'})

        # 按时间排序
        points.sort(key=lambda x: x['index'])

        # 构建笔
        pen_lines = []
        if len(points) >= 2:
            for i in range(len(points) - 1):
                start = points[i]
                end = points[i + 1]

                # 只有顶底交替才构成笔
                if start['type'] != end['type']:
                    pen_lines.append({
                        'start_index': start['index'],
                        'end_index': end['index'],
                        'start_price': start['price'],
                        'end_price': end['price'],
                        'direction': 'up' if end['price'] > start['price'] else 'down'
                    })

        return pd.DataFrame(pen_lines)

    @staticmethod
    def find_central_zones(pen_lines: pd.DataFrame) -> pd.DataFrame:
        """
        寻找中枢（至少三笔重叠的区域）
        """
        if len(pen_lines) < 3:
            return pd.DataFrame()

        central_zones = []

        for i in range(len(pen_lines) - 2):
            # 取连续三笔
            pen1 = pen_lines.iloc[i]
            pen2 = pen_lines.iloc[i + 1]
            pen3 = pen_lines.iloc[i + 2]

            # 计算重叠区域
            high_prices = [pen1['start_price'], pen1['end_price'],
                          pen2['start_price'], pen2['end_price'],
                          pen3['start_price'], pen3['end_price']]
            low_prices = high_prices.copy()

            zone_high = min(max(pen1['start_price'], pen1['end_price']),
                           max(pen2['start_price'], pen2['end_price']),
                           max(pen3['start_price'], pen3['end_price']))
            zone_low = max(min(pen1['start_price'], pen1['end_price']),
                          min(pen2['start_price'], pen2['end_price']),
                          min(pen3['start_price'], pen3['end_price']))

            # 如果有重叠区域，则形成中枢
            if zone_high > zone_low:
                central_zones.append({
                    'start_index': pen1['start_index'],
                    'end_index': pen3['end_index'],
                    'zone_high': zone_high,
                    'zone_low': zone_low,
                    'zone_mid': (zone_high + zone_low) / 2
                })

        return pd.DataFrame(central_zones)

    @staticmethod
    def calculate_trend_strength(close: pd.Series, pen_lines: pd.DataFrame) -> pd.Series:
        """
        计算趋势强度
        """
        strength = pd.Series(index=close.index, dtype=float)

        if len(pen_lines) == 0:
            return strength

        for i in range(len(close)):
            # 找到当前位置对应的笔
            current_pen = None
            for _, pen in pen_lines.iterrows():
                if pen['start_index'] <= i <= pen['end_index']:
                    current_pen = pen
                    break

            if current_pen is not None:
                # 计算当前价格在笔中的位置比例
                pen_range = abs(current_pen['end_price'] - current_pen['start_price'])
                if pen_range > 0:
                    if current_pen['direction'] == 'up':
                        progress = (close.iloc[i] - current_pen['start_price']) / pen_range
                    else:
                        progress = (current_pen['start_price'] - close.iloc[i]) / pen_range

                    strength.iloc[i] = max(0, min(1, progress))

        return strength

# 缠论便捷函数
def MERGE_K_LINES(high: pd.Series, low: pd.Series, close: pd.Series, open_price: pd.Series) -> pd.DataFrame:
    """K线合并"""
    return ChanLunIndicators.merge_k_lines(high, low, close, open_price)

def FIND_FRACTAL_POINTS(high: pd.Series, low: pd.Series) -> Tuple[pd.Series, pd.Series]:
    """寻找分型点"""
    return ChanLunIndicators.find_fractal_points(high, low)

def FIND_PEN_LINES(tops: pd.Series, bottoms: pd.Series) -> pd.DataFrame:
    """寻找笔"""
    return ChanLunIndicators.find_pen_lines(tops, bottoms)

def FIND_CENTRAL_ZONES(pen_lines: pd.DataFrame) -> pd.DataFrame:
    """寻找中枢"""
    return ChanLunIndicators.find_central_zones(pen_lines)

def CALCULATE_TREND_STRENGTH(close: pd.Series, pen_lines: pd.DataFrame) -> pd.Series:
    """计算趋势强度"""
    return ChanLunIndicators.calculate_trend_strength(close, pen_lines)

# 基础划线函数
def DRAWLINE(x1: float, y1: float, x2: float, y2: float, color: str = 'blue', width: int = 1) -> dict:
    """绘制直线"""
    return {
        'type': 'line',
        'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
        'color': color, 'width': width
    }

def DRAWTEXT(x: float, y: float, text: str, color: str = 'black', size: int = 10) -> dict:
    """绘制文本"""
    return {
        'type': 'text',
        'x': x, 'y': y, 'text': text,
        'color': color, 'size': size
    }

def DRAWICON(x: float, y: float, icon: str, color: str = 'red') -> dict:
    """绘制图标"""
    return {
        'type': 'icon',
        'x': x, 'y': y, 'icon': icon,
        'color': color
    }

def STICKLINE(condition: pd.Series, price1: pd.Series, price2: pd.Series,
             width: int = 1, empty: bool = False, color: str = 'red') -> dict:
    """绘制柱状线"""
    return {
        'type': 'stickline',
        'condition': condition,
        'price1': price1, 'price2': price2,
        'width': width, 'empty': empty, 'color': color
    }

# 高级技术指标
def BIAS(close: pd.Series, period: int = 6) -> pd.Series:
    """乖离率"""
    ma = close.rolling(period).mean()
    return (close - ma) / ma * 100

def PSY(close: pd.Series, period: int = 12) -> pd.Series:
    """心理线指标"""
    up_days = (close > close.shift(1)).rolling(period).sum()
    return up_days / period * 100

def VR(close: pd.Series, volume: pd.Series, period: int = 26) -> pd.Series:
    """成交量比率"""
    up_volume = volume.where(close > close.shift(1), 0).rolling(period).sum()
    down_volume = volume.where(close < close.shift(1), 0).rolling(period).sum()
    equal_volume = volume.where(close == close.shift(1), 0).rolling(period).sum()

    return (up_volume + equal_volume / 2) / (down_volume + equal_volume / 2) * 100

def ARBR(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 26) -> tuple:
    """AR BR指标"""
    # AR指标
    ho = high - close.shift(1)
    lo = close.shift(1) - low
    ar = ho.rolling(period).sum() / lo.rolling(period).sum() * 100

    # BR指标
    hc = high - close.shift(1)
    cl = close.shift(1) - low
    br = hc.rolling(period).sum() / cl.rolling(period).sum() * 100

    return ar, br

def DMI(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> tuple:
    """动向指标"""
    tr = pd.concat([
        high - low,
        abs(high - close.shift(1)),
        abs(low - close.shift(1))
    ], axis=1).max(axis=1)

    hd = high - high.shift(1)
    ld = low.shift(1) - low

    dmp = hd.where((hd > ld) & (hd > 0), 0)
    dmm = ld.where((ld > hd) & (ld > 0), 0)

    tr_sum = tr.rolling(period).sum()
    dmp_sum = dmp.rolling(period).sum()
    dmm_sum = dmm.rolling(period).sum()

    pdi = dmp_sum / tr_sum * 100
    mdi = dmm_sum / tr_sum * 100
    adx = abs(pdi - mdi) / (pdi + mdi) * 100
    adx = adx.rolling(period).mean()

    return pdi, mdi, adx

def TRIX(close: pd.Series, period: int = 12) -> pd.Series:
    """三重指数平滑移动平均"""
    ema1 = close.ewm(span=period).mean()
    ema2 = ema1.ewm(span=period).mean()
    ema3 = ema2.ewm(span=period).mean()

    return ema3.pct_change() * 10000

def DPO(close: pd.Series, period: int = 20) -> pd.Series:
    """区间震荡线"""
    ma = close.rolling(period).mean()
    return close.shift(period // 2 + 1) - ma

def EMV(high: pd.Series, low: pd.Series, volume: pd.Series, period: int = 14) -> pd.Series:
    """简易波动指标"""
    distance = (high + low) / 2 - (high.shift(1) + low.shift(1)) / 2
    box_ratio = volume / (high - low)
    emv_raw = distance / box_ratio
    return emv_raw.rolling(period).mean()

def MASS(high: pd.Series, low: pd.Series, period1: int = 9, period2: int = 25) -> pd.Series:
    """梅斯线"""
    hl_ratio = (high - low) / (high - low).ewm(span=period1).mean()
    return hl_ratio.rolling(period2).sum()

def CHAIKIN(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series,
           fast: int = 3, slow: int = 10) -> pd.Series:
    """佳庆指标"""
    ad = ((close - low) - (high - close)) / (high - low) * volume
    return ad.ewm(span=fast).mean() - ad.ewm(span=slow).mean()

def ULTIMATE(high: pd.Series, low: pd.Series, close: pd.Series,
            period1: int = 7, period2: int = 14, period3: int = 28) -> pd.Series:
    """终极指标"""
    tr = pd.concat([
        high - low,
        abs(high - close.shift(1)),
        abs(low - close.shift(1))
    ], axis=1).max(axis=1)

    bp = close - pd.concat([low, close.shift(1)], axis=1).min(axis=1)

    avg1 = bp.rolling(period1).sum() / tr.rolling(period1).sum()
    avg2 = bp.rolling(period2).sum() / tr.rolling(period2).sum()
    avg3 = bp.rolling(period3).sum() / tr.rolling(period3).sum()

    return (4 * avg1 + 2 * avg2 + avg3) / 7 * 100
