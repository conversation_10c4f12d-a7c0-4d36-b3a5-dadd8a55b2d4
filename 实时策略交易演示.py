#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时策略交易演示程序
演示实时数据更新、策略信号生成和自动交易功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from datetime import datetime, timedelta
import threading
import time
import random

class RealtimeStrategyDemo:
    """实时策略交易演示类"""
    
    def __init__(self, master):
        self.master = master
        master.title("实时策略交易演示")
        master.geometry("1000x700")
        
        # 模拟数据
        self.df = None
        self.current_price = 10.0
        self.current_holdings = 0
        self.available_funds = 100000.0
        self.strategy_signals = []
        self.trade_history = []
        
        # 实时更新控制
        self.realtime_running = False
        self.realtime_thread = None
        self.update_interval = 3  # 3秒更新一次（演示用）
        
        # 策略参数
        self.strategy_type = "MACD"
        self.auto_trading_enabled = False
        
        # 创建界面
        self.create_widgets()
        
        # 初始化模拟数据
        self.initialize_demo_data()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = tk.LabelFrame(main_frame, text="实时策略控制", padx=10, pady=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 第一行控制
        row1_frame = tk.Frame(control_frame)
        row1_frame.pack(fill=tk.X, pady=5)
        
        # 实时数据控制
        self.realtime_var = tk.BooleanVar(value=False)
        self.realtime_check = tk.Checkbutton(row1_frame, text="启用实时数据",
                                           variable=self.realtime_var,
                                           command=self.toggle_realtime_data)
        self.realtime_check.pack(side=tk.LEFT, padx=5)
        
        # 策略选择
        tk.Label(row1_frame, text="策略:").pack(side=tk.LEFT, padx=5)
        self.strategy_var = tk.StringVar(value="MACD")
        strategy_combo = ttk.Combobox(row1_frame, textvariable=self.strategy_var,
                                    values=["MACD", "KDJ", "均线"], width=10)
        strategy_combo.pack(side=tk.LEFT, padx=5)
        strategy_combo.bind('<<ComboboxSelected>>', self.on_strategy_change)
        
        # 自动交易控制
        self.auto_trading_var = tk.BooleanVar(value=False)
        self.auto_trading_check = tk.Checkbutton(row1_frame, text="启用自动交易",
                                                variable=self.auto_trading_var,
                                                command=self.toggle_auto_trading)
        self.auto_trading_check.pack(side=tk.LEFT, padx=10)
        
        # 第二行状态显示
        row2_frame = tk.Frame(control_frame)
        row2_frame.pack(fill=tk.X, pady=5)
        
        self.realtime_status_label = tk.Label(row2_frame, text="实时数据: 未启动", fg='gray')
        self.realtime_status_label.pack(side=tk.LEFT, padx=5)
        
        self.strategy_status_label = tk.Label(row2_frame, text="策略状态: 等待", fg='gray')
        self.strategy_status_label.pack(side=tk.LEFT, padx=10)
        
        self.trading_status_label = tk.Label(row2_frame, text="自动交易: 未启用", fg='gray')
        self.trading_status_label.pack(side=tk.LEFT, padx=10)
        
        # 第三行信息显示
        row3_frame = tk.Frame(control_frame)
        row3_frame.pack(fill=tk.X, pady=5)
        
        self.price_label = tk.Label(row3_frame, text="当前价格: --", fg='blue')
        self.price_label.pack(side=tk.LEFT, padx=5)
        
        self.holdings_label = tk.Label(row3_frame, text="持仓: 0股", fg='green')
        self.holdings_label.pack(side=tk.LEFT, padx=10)
        
        self.funds_label = tk.Label(row3_frame, text="资金: ¥100,000", fg='green')
        self.funds_label.pack(side=tk.LEFT, padx=10)
        
        self.signal_label = tk.Label(row3_frame, text="最新信号: 无", fg='orange')
        self.signal_label.pack(side=tk.LEFT, padx=10)
        
        # 图表和日志区域
        content_frame = tk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧图表
        chart_frame = tk.LabelFrame(content_frame, text="实时K线图与策略信号", padx=5, pady=5)
        chart_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 创建图表
        self.figure = Figure(figsize=(8, 6), dpi=80)
        self.canvas = FigureCanvasTkAgg(self.figure, master=chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 右侧日志
        log_frame = tk.LabelFrame(content_frame, text="交易日志", padx=5, pady=5)
        log_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # 交易日志
        self.log_text = tk.Text(log_frame, width=40, height=20)
        log_scrollbar = tk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")
        
        # 底部统计信息
        stats_frame = tk.LabelFrame(main_frame, text="交易统计", padx=10, pady=5)
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.stats_label = tk.Label(stats_frame, text="总交易次数: 0 | 盈利次数: 0 | 亏损次数: 0 | 总盈亏: ¥0.00")
        self.stats_label.pack()
        
    def initialize_demo_data(self):
        """初始化演示数据"""
        # 生成30天的历史数据
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), 
                             end=datetime.now(), freq='D')
        
        # 生成模拟价格数据
        np.random.seed(42)  # 固定随机种子，确保结果可重现
        prices = []
        price = 10.0
        
        for i in range(len(dates)):
            # 随机游走生成价格
            change = np.random.normal(0, 0.02)  # 2%的日波动
            price = max(price * (1 + change), 5.0)  # 最低价格5元
            prices.append(price)
        
        # 创建DataFrame
        self.df = pd.DataFrame({
            'trade_date': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'vol': [np.random.randint(1000000, 5000000) for _ in prices]
        })
        
        # 计算技术指标
        self.calculate_indicators()
        
        # 设置当前价格
        self.current_price = prices[-1]
        self.update_price_display()
        
        # 绘制初始图表
        self.update_chart()
        
        # 记录初始日志
        self.log_message("📊 演示系统已初始化")
        self.log_message(f"💰 初始资金: ¥{self.available_funds:,.2f}")
        self.log_message(f"📈 当前价格: ¥{self.current_price:.2f}")
        
    def calculate_indicators(self):
        """计算技术指标"""
        # 计算MACD
        exp1 = self.df['close'].ewm(span=12).mean()
        exp2 = self.df['close'].ewm(span=26).mean()
        self.df['macd'] = exp1 - exp2
        self.df['macd_signal'] = self.df['macd'].ewm(span=9).mean()
        self.df['macd_hist'] = self.df['macd'] - self.df['macd_signal']
        
        # 计算KDJ
        low_min = self.df['low'].rolling(window=9).min()
        high_max = self.df['high'].rolling(window=9).max()
        rsv = (self.df['close'] - low_min) / (high_max - low_min) * 100
        self.df['k'] = rsv.ewm(com=2).mean()
        self.df['d'] = self.df['k'].ewm(com=2).mean()
        self.df['j'] = 3 * self.df['k'] - 2 * self.df['d']
        
        # 计算均线
        self.df['ma5'] = self.df['close'].rolling(window=5).mean()
        self.df['ma20'] = self.df['close'].rolling(window=20).mean()
        
    def generate_strategy_signals(self):
        """生成策略信号"""
        if self.df is None or len(self.df) < 2:
            return None
        
        strategy = self.strategy_var.get()
        latest_idx = len(self.df) - 1
        prev_idx = latest_idx - 1
        
        signal = None
        signal_strength = 0
        
        if strategy == "MACD":
            # MACD策略：MACD线上穿信号线买入，下穿卖出
            current_macd = self.df.iloc[latest_idx]['macd']
            current_signal = self.df.iloc[latest_idx]['macd_signal']
            prev_macd = self.df.iloc[prev_idx]['macd']
            prev_signal = self.df.iloc[prev_idx]['macd_signal']
            
            if prev_macd <= prev_signal and current_macd > current_signal:
                signal = "BUY"
                signal_strength = abs(current_macd - current_signal) * 100
            elif prev_macd >= prev_signal and current_macd < current_signal:
                signal = "SELL"
                signal_strength = abs(current_macd - current_signal) * 100
                
        elif strategy == "KDJ":
            # KDJ策略：K线上穿D线且在超卖区买入，下穿且在超买区卖出
            current_k = self.df.iloc[latest_idx]['k']
            current_d = self.df.iloc[latest_idx]['d']
            prev_k = self.df.iloc[prev_idx]['k']
            prev_d = self.df.iloc[prev_idx]['d']
            
            if prev_k <= prev_d and current_k > current_d and current_k < 30:
                signal = "BUY"
                signal_strength = min(current_k, current_d)
            elif prev_k >= prev_d and current_k < current_d and current_k > 70:
                signal = "SELL"
                signal_strength = 100 - max(current_k, current_d)
                
        elif strategy == "均线":
            # 均线策略：价格上穿MA5买入，下穿卖出
            current_price = self.df.iloc[latest_idx]['close']
            current_ma5 = self.df.iloc[latest_idx]['ma5']
            prev_price = self.df.iloc[prev_idx]['close']
            prev_ma5 = self.df.iloc[prev_idx]['ma5']
            
            if prev_price <= prev_ma5 and current_price > current_ma5:
                signal = "BUY"
                signal_strength = (current_price - current_ma5) / current_ma5 * 100
            elif prev_price >= prev_ma5 and current_price < current_ma5:
                signal = "SELL"
                signal_strength = (current_ma5 - current_price) / current_ma5 * 100
        
        if signal:
            signal_info = {
                'time': datetime.now().strftime('%H:%M:%S'),
                'signal': signal,
                'price': self.current_price,
                'strength': signal_strength,
                'strategy': strategy
            }
            self.strategy_signals.append(signal_info)
            return signal_info
        
        return None
    
    def toggle_realtime_data(self):
        """切换实时数据"""
        if self.realtime_var.get():
            self.start_realtime_data()
        else:
            self.stop_realtime_data()
    
    def start_realtime_data(self):
        """启动实时数据"""
        self.realtime_running = True
        self.realtime_thread = threading.Thread(target=self.realtime_worker, daemon=True)
        self.realtime_thread.start()
        
        self.realtime_status_label.config(text="实时数据: 运行中", fg='green')
        self.log_message("🔄 实时数据获取已启动")
    
    def stop_realtime_data(self):
        """停止实时数据"""
        self.realtime_running = False
        self.realtime_status_label.config(text="实时数据: 已停止", fg='red')
        self.log_message("⏹️ 实时数据获取已停止")
    
    def realtime_worker(self):
        """实时数据工作线程"""
        while self.realtime_running:
            try:
                # 模拟价格变化
                change = np.random.normal(0, 0.005)  # 0.5%的变化
                self.current_price = max(self.current_price * (1 + change), 5.0)
                
                # 添加新的数据点
                new_row = {
                    'trade_date': datetime.now(),
                    'open': self.current_price,
                    'high': self.current_price * (1 + abs(np.random.normal(0, 0.002))),
                    'low': self.current_price * (1 - abs(np.random.normal(0, 0.002))),
                    'close': self.current_price,
                    'vol': np.random.randint(1000000, 3000000)
                }
                
                # 更新数据
                self.master.after(0, self.update_realtime_data, new_row)
                
                # 等待更新间隔
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"实时数据更新错误: {str(e)}")
                time.sleep(1)
    
    def update_realtime_data(self, new_row):
        """更新实时数据"""
        try:
            # 添加新数据
            self.df = pd.concat([self.df, pd.DataFrame([new_row])], ignore_index=True)
            
            # 保持最近50个数据点
            if len(self.df) > 50:
                self.df = self.df.tail(50).reset_index(drop=True)
            
            # 重新计算指标
            self.calculate_indicators()
            
            # 更新价格显示
            self.update_price_display()
            
            # 生成策略信号
            signal_info = self.generate_strategy_signals()
            
            if signal_info:
                self.strategy_status_label.config(
                    text=f"策略状态: {signal_info['signal']} 信号",
                    fg='red' if signal_info['signal'] == 'SELL' else 'green'
                )
                
                self.signal_label.config(
                    text=f"最新信号: {signal_info['time']} {signal_info['signal']} @ ¥{signal_info['price']:.2f}",
                    fg='red' if signal_info['signal'] == 'SELL' else 'green'
                )
                
                self.log_message(f"📊 {signal_info['strategy']}策略信号: {signal_info['signal']} @ ¥{signal_info['price']:.2f}")
                
                # 如果启用自动交易，执行交易
                if self.auto_trading_var.get():
                    self.execute_auto_trade(signal_info)
            
            # 更新图表
            self.update_chart()
            
        except Exception as e:
            print(f"更新实时数据错误: {str(e)}")
    
    def execute_auto_trade(self, signal_info):
        """执行自动交易"""
        try:
            signal = signal_info['signal']
            price = signal_info['price']
            
            if signal == 'BUY' and self.current_holdings == 0:
                # 买入
                trade_amount = min(self.available_funds * 0.8, 50000)  # 最多用80%资金或5万
                quantity = int(trade_amount / price / 100) * 100  # 按手计算
                
                if quantity >= 100:
                    cost = quantity * price
                    self.current_holdings = quantity
                    self.available_funds -= cost
                    
                    trade_record = {
                        'time': datetime.now().strftime('%H:%M:%S'),
                        'action': 'BUY',
                        'quantity': quantity,
                        'price': price,
                        'amount': cost
                    }
                    self.trade_history.append(trade_record)
                    
                    self.log_message(f"🤖 自动买入: {quantity}股 @ ¥{price:.2f}, 成本: ¥{cost:,.2f}")
                    self.update_holdings_display()
                    self.update_stats()
            
            elif signal == 'SELL' and self.current_holdings > 0:
                # 卖出
                quantity = self.current_holdings
                revenue = quantity * price
                self.available_funds += revenue
                self.current_holdings = 0
                
                trade_record = {
                    'time': datetime.now().strftime('%H:%M:%S'),
                    'action': 'SELL',
                    'quantity': quantity,
                    'price': price,
                    'amount': revenue
                }
                self.trade_history.append(trade_record)
                
                self.log_message(f"🤖 自动卖出: {quantity}股 @ ¥{price:.2f}, 收入: ¥{revenue:,.2f}")
                self.update_holdings_display()
                self.update_stats()
                
        except Exception as e:
            self.log_message(f"❌ 自动交易失败: {str(e)}")
    
    def update_price_display(self):
        """更新价格显示"""
        self.price_label.config(text=f"当前价格: ¥{self.current_price:.2f}")
    
    def update_holdings_display(self):
        """更新持仓显示"""
        self.holdings_label.config(text=f"持仓: {self.current_holdings}股")
        self.funds_label.config(text=f"资金: ¥{self.available_funds:,.2f}")
    
    def update_stats(self):
        """更新统计信息"""
        if len(self.trade_history) < 2:
            return
        
        # 计算盈亏
        buy_trades = [t for t in self.trade_history if t['action'] == 'BUY']
        sell_trades = [t for t in self.trade_history if t['action'] == 'SELL']
        
        total_trades = min(len(buy_trades), len(sell_trades))
        profit_trades = 0
        loss_trades = 0
        total_pnl = 0
        
        for i in range(total_trades):
            buy_price = buy_trades[i]['price']
            sell_price = sell_trades[i]['price']
            quantity = buy_trades[i]['quantity']
            
            pnl = (sell_price - buy_price) * quantity
            total_pnl += pnl
            
            if pnl > 0:
                profit_trades += 1
            else:
                loss_trades += 1
        
        self.stats_label.config(
            text=f"总交易次数: {total_trades} | 盈利次数: {profit_trades} | 亏损次数: {loss_trades} | 总盈亏: ¥{total_pnl:,.2f}"
        )
    
    def toggle_auto_trading(self):
        """切换自动交易"""
        if self.auto_trading_var.get():
            self.trading_status_label.config(text="自动交易: 已启用", fg='green')
            self.log_message("🤖 自动交易已启用")
        else:
            self.trading_status_label.config(text="自动交易: 已禁用", fg='gray')
            self.log_message("⏹️ 自动交易已禁用")
    
    def on_strategy_change(self, event=None):
        """策略变化处理"""
        strategy = self.strategy_var.get()
        self.log_message(f"📈 策略已切换为: {strategy}")
        self.strategy_status_label.config(text=f"策略状态: {strategy}策略", fg='blue')
    
    def update_chart(self):
        """更新图表"""
        if self.df is None or len(self.df) < 10:
            return
        
        self.figure.clear()
        
        # 使用最近30个数据点
        plot_data = self.df.tail(30)
        dates = plot_data['trade_date']
        
        # 创建子图
        ax1 = self.figure.add_subplot(211)
        ax2 = self.figure.add_subplot(212)
        
        # 绘制K线图（简化为线图）
        ax1.plot(dates, plot_data['close'], 'b-', linewidth=1, label='价格')
        
        # 绘制均线
        if 'ma5' in plot_data.columns:
            ax1.plot(dates, plot_data['ma5'], 'r--', alpha=0.7, label='MA5')
        if 'ma20' in plot_data.columns:
            ax1.plot(dates, plot_data['ma20'], 'g--', alpha=0.7, label='MA20')
        
        # 绘制买卖点
        for signal in self.strategy_signals[-10:]:  # 最近10个信号
            signal_time = datetime.strptime(signal['time'], '%H:%M:%S').replace(
                year=datetime.now().year,
                month=datetime.now().month,
                day=datetime.now().day
            )
            
            if signal['signal'] == 'BUY':
                ax1.scatter(signal_time, signal['price'], color='red', marker='^', s=100, label='买入' if signal == self.strategy_signals[-10] else "")
            else:
                ax1.scatter(signal_time, signal['price'], color='green', marker='v', s=100, label='卖出' if signal == self.strategy_signals[-10] else "")
        
        ax1.set_title('实时价格与策略信号')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 绘制技术指标
        strategy = self.strategy_var.get()
        if strategy == "MACD":
            ax2.plot(dates, plot_data['macd'], 'b-', label='MACD')
            ax2.plot(dates, plot_data['macd_signal'], 'r-', label='Signal')
            ax2.bar(dates, plot_data['macd_hist'], alpha=0.3, label='Histogram')
            ax2.set_title('MACD指标')
        elif strategy == "KDJ":
            ax2.plot(dates, plot_data['k'], 'b-', label='K')
            ax2.plot(dates, plot_data['d'], 'r-', label='D')
            ax2.plot(dates, plot_data['j'], 'g-', label='J')
            ax2.axhline(y=20, color='gray', linestyle='--', alpha=0.5)
            ax2.axhline(y=80, color='gray', linestyle='--', alpha=0.5)
            ax2.set_title('KDJ指标')
        
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        self.figure.tight_layout()
        self.canvas.draw()
    
    def log_message(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

def main():
    """主函数"""
    print("启动实时策略交易演示...")
    
    root = tk.Tk()
    app = RealtimeStrategyDemo(root)
    
    print("✅ 演示程序已启动")
    print("🔄 请启用实时数据和自动交易来查看效果")
    
    root.mainloop()

if __name__ == "__main__":
    main()
