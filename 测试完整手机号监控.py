#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整手机号和自定义策略的监控功能
"""

import pandas as pd
import numpy as np
from datetime import datetime
from 使用者监控 import UserMonitor, set_user_phone, monitor_backtest_completion

def create_test_backtest_results():
    """创建测试回测结果"""
    # 模拟资产曲线数据
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    initial_capital = 100000
    
    # 模拟价格变化
    np.random.seed(42)
    daily_returns = np.random.normal(0.001, 0.02, len(dates))
    cumulative_returns = np.cumprod(1 + daily_returns)
    total_values = initial_capital * cumulative_returns
    
    equity_curve = pd.DataFrame({
        'date': dates,
        'total_value': total_values,
        'cash': total_values * 0.1,
        'positions_value': total_values * 0.9,
        'daily_return': daily_returns
    })
    
    # 模拟交易记录
    trades_data = []
    for i in range(5):  # 减少交易数量以便查看
        buy_date = dates[i * 60]
        sell_date = dates[i * 60 + 30] if i * 60 + 30 < len(dates) else dates[-1]
        buy_price = 10 + np.random.random() * 5
        sell_price = buy_price * (1 + np.random.normal(0.05, 0.1))
        
        trades_data.extend([
            {
                'symbol': 'TEST',
                'action': 'BUY',
                'quantity': 1000,
                'price': buy_price,
                'date': buy_date.strftime('%Y-%m-%d'),
                'commission': buy_price * 1000 * 0.001,
                'value': buy_price * 1000
            },
            {
                'symbol': 'TEST',
                'action': 'SELL',
                'quantity': 1000,
                'price': sell_price,
                'date': sell_date.strftime('%Y-%m-%d'),
                'commission': sell_price * 1000 * 0.001,
                'value': sell_price * 1000
            }
        ])
    
    trades = pd.DataFrame(trades_data)
    
    # 计算回测指标
    final_value = total_values[-1]
    total_return = (final_value - initial_capital) / initial_capital
    trading_days = len(dates)
    annual_return = (final_value / initial_capital) ** (252 / trading_days) - 1
    volatility = equity_curve['daily_return'].std() * np.sqrt(252)
    
    # 计算最大回撤
    rolling_max = equity_curve['total_value'].expanding().max()
    drawdown = (equity_curve['total_value'] - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # 计算夏普比率
    risk_free_rate = 0.03
    sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
    
    # 计算交易统计
    buy_trades = trades[trades['action'] == 'BUY']
    sell_trades = trades[trades['action'] == 'SELL']
    total_trades = len(buy_trades)
    
    # 计算胜率
    trade_returns = []
    for i in range(min(len(buy_trades), len(sell_trades))):
        buy_price = buy_trades.iloc[i]['price']
        sell_price = sell_trades.iloc[i]['price']
        trade_return = (sell_price - buy_price) / buy_price
        trade_returns.append(trade_return)
    
    win_trades = len([r for r in trade_returns if r > 0])
    win_rate = win_trades / len(trade_returns) if trade_returns else 0
    
    results = {
        'initial_capital': initial_capital,
        'final_value': final_value,
        'total_return': total_return,
        'annual_return': annual_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'total_trades': total_trades,
        'win_rate': win_rate,
        'equity_curve': equity_curve,
        'trades': trades
    }
    
    return results

def create_complex_custom_strategy():
    """创建复杂的自定义策略代码"""
    return """
# 复合技术指标策略
# 结合多个技术指标进行交易决策

import pandas as pd
import numpy as np

# 1. 计算移动平均线
data['MA5'] = data['close'].rolling(window=5).mean()
data['MA10'] = data['close'].rolling(window=10).mean()
data['MA20'] = data['close'].rolling(window=20).mean()

# 2. 计算RSI指标
def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

data['RSI'] = calculate_rsi(data['close'])

# 3. 计算MACD指标
def calculate_macd(prices, fast=12, slow=26, signal=9):
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    signal_line = macd.ewm(span=signal).mean()
    histogram = macd - signal_line
    return macd, signal_line, histogram

data['MACD'], data['MACD_signal'], data['MACD_hist'] = calculate_macd(data['close'])

# 4. 计算布林带
data['BB_middle'] = data['close'].rolling(window=20).mean()
data['BB_std'] = data['close'].rolling(window=20).std()
data['BB_upper'] = data['BB_middle'] + (data['BB_std'] * 2)
data['BB_lower'] = data['BB_middle'] - (data['BB_std'] * 2)

# 5. 生成复合交易信号
data['signal'] = 0

# 买入条件：
# - 短期均线上穿长期均线
# - RSI < 70 (不超买)
# - MACD > 0 (趋势向上)
# - 价格接近布林带下轨 (相对低位)
buy_condition = (
    (data['MA5'] > data['MA20']) &  # 短期趋势向上
    (data['MA5'].shift(1) <= data['MA20'].shift(1)) &  # 刚刚突破
    (data['RSI'] < 70) &  # 不超买
    (data['MACD'] > 0) &  # MACD为正
    (data['close'] < data['BB_middle'])  # 价格在中轨以下
)

# 卖出条件：
# - 短期均线下穿长期均线
# - RSI > 30 (不超卖)
# - 价格接近布林带上轨 (相对高位)
sell_condition = (
    (data['MA5'] < data['MA20']) &  # 短期趋势向下
    (data['MA5'].shift(1) >= data['MA20'].shift(1)) &  # 刚刚跌破
    (data['RSI'] > 30) &  # 不超卖
    (data['close'] > data['BB_middle'])  # 价格在中轨以上
)

# 应用交易信号
data.loc[buy_condition, 'signal'] = 1   # 买入信号
data.loc[sell_condition, 'signal'] = -1  # 卖出信号

# 计算持仓
data['position'] = data['signal'].shift(1).fillna(0)

# 风险控制：连续亏损超过3次则暂停交易
data['returns'] = data['close'].pct_change()
data['strategy_returns'] = data['position'] * data['returns']
data['cumulative_returns'] = (1 + data['strategy_returns']).cumprod()

# 添加止损逻辑
data['max_cumulative'] = data['cumulative_returns'].expanding().max()
data['drawdown'] = (data['cumulative_returns'] - data['max_cumulative']) / data['max_cumulative']

# 如果回撤超过10%，则清仓
stop_loss_condition = data['drawdown'] < -0.10
data.loc[stop_loss_condition, 'signal'] = -1
data.loc[stop_loss_condition, 'position'] = 0

print("复合技术指标策略已应用")
print(f"总买入信号: {(data['signal'] == 1).sum()}")
print(f"总卖出信号: {(data['signal'] == -1).sum()}")
"""

def test_full_phone_monitoring():
    """测试完整手机号监控功能"""
    print("=" * 70)
    print("测试完整手机号和自定义策略监控功能")
    print("=" * 70)
    
    # 1. 测试设置完整手机号
    print("\n1. 测试设置完整手机号...")
    test_phone = "13812345678"
    set_user_phone(test_phone)
    print(f"设置完整手机号: {test_phone}")
    
    # 2. 验证保存的数据
    print("\n2. 验证保存的数据...")
    monitor = UserMonitor()
    full_phone = monitor.get_phone_number()
    phone_suffix = monitor.get_phone_suffix()
    print(f"保存的完整手机号: {full_phone}")
    print(f"保存的手机号后四位: {phone_suffix}")
    
    # 3. 创建复杂的回测结果和策略
    print("\n3. 创建测试数据...")
    backtest_results = create_test_backtest_results()
    custom_strategy_code = create_complex_custom_strategy()
    print(f"回测结果创建完成，总收益率: {backtest_results['total_return']:.2%}")
    print(f"自定义策略代码长度: {len(custom_strategy_code)} 字符")
    
    # 4. 测试数据收集
    print("\n4. 测试数据收集...")
    collected_data = monitor.collect_backtest_data(backtest_results, custom_strategy_code)
    print(f"收集的手机号: {collected_data.get('phone_number', '未找到')}")
    print(f"收集的策略代码长度: {len(collected_data.get('custom_strategy_code', ''))}")
    
    # 5. 测试XLSX文件生成
    print("\n5. 测试XLSX文件生成...")
    filepath = monitor.create_xlsx_file(collected_data)
    if filepath:
        print(f"XLSX文件生成成功: {filepath}")
        
        # 验证文件内容
        try:
            import os
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"文件大小: {file_size} 字节")
                
                # 读取并验证Excel内容
                print("\n验证Excel文件内容:")
                
                # 读取回测结果表
                df_results = pd.read_excel(filepath, sheet_name='回测结果')
                print(f"回测结果表 - 手机号: {df_results.iloc[0]['手机号']}")
                
                # 读取自定义公式表
                df_strategy = pd.read_excel(filepath, sheet_name='自定义公式')
                strategy_code = df_strategy.iloc[0]['自定义策略代码']
                print(f"自定义公式表 - 手机号: {df_strategy.iloc[0]['手机号']}")
                print(f"自定义公式表 - 策略代码前100字符: {strategy_code[:100]}...")
                
            else:
                print("文件不存在")
        except Exception as e:
            print(f"验证文件时出错: {e}")
    else:
        print("XLSX文件生成失败")
    
    # 6. 测试完整流程
    print("\n6. 测试完整监控流程...")
    success = monitor_backtest_completion(backtest_results, custom_strategy_code)
    if success:
        print("✅ 完整监控流程测试成功")
    else:
        print("❌ 完整监控流程测试失败")
    
    print("\n" + "=" * 70)
    print("完整手机号和自定义策略监控功能测试完成")
    print("=" * 70)

if __name__ == "__main__":
    test_full_phone_monitoring()
