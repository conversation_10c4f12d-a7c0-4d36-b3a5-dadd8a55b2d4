#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易调度器
管理多股票的交易信号队列，避免同时执行过多交易操作
"""

import threading
import time
import queue
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradeAction(Enum):
    """交易动作枚举"""
    BUY = "BUY"
    SELL = "SELL"


class TradeSignal:
    """交易信号类"""
    
    def __init__(self, ts_code: str, action: TradeAction, price: float, 
                 quantity: int = 0, priority: int = 1, strategy_type: str = ""):
        self.ts_code = ts_code
        self.action = action
        self.price = price
        self.quantity = quantity
        self.priority = priority  # 优先级，数字越小优先级越高
        self.strategy_type = strategy_type
        
        self.created_time = datetime.now()
        self.scheduled_time = None
        self.executed_time = None
        self.status = "PENDING"  # PENDING, SCHEDULED, EXECUTING, COMPLETED, FAILED, CANCELLED
        self.error_message = ""
        self.retry_count = 0
        self.max_retries = 3
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        if self.priority != other.priority:
            return self.priority < other.priority
        return self.created_time < other.created_time
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'ts_code': self.ts_code,
            'action': self.action.value,
            'price': self.price,
            'quantity': self.quantity,
            'priority': self.priority,
            'strategy_type': self.strategy_type,
            'created_time': self.created_time,
            'scheduled_time': self.scheduled_time,
            'executed_time': self.executed_time,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }


class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        # 风险控制参数
        self.max_position_per_stock = 10000  # 单股票最大持仓数量
        self.max_total_positions = 50  # 最大持仓股票数量
        self.max_daily_trades = 100  # 每日最大交易次数
        self.max_trade_amount = 100000  # 单笔最大交易金额
        self.min_trade_interval = 60  # 同一股票最小交易间隔（秒）
        
        # 当前状态
        self.current_positions: Dict[str, int] = {}  # 当前持仓
        self.daily_trade_count = 0
        self.last_trade_times: Dict[str, datetime] = {}  # 最后交易时间
        self.available_funds = 0.0  # 可用资金
        
        # 重置每日计数器
        self.last_reset_date = datetime.now().date()
    
    def check_trade_allowed(self, signal: TradeSignal) -> tuple[bool, str]:
        """检查交易是否被允许"""
        # 重置每日计数器
        self._reset_daily_counter_if_needed()
        
        # 检查每日交易次数限制
        if self.daily_trade_count >= self.max_daily_trades:
            return False, f"已达到每日最大交易次数限制: {self.max_daily_trades}"
        
        # 检查交易间隔
        if signal.ts_code in self.last_trade_times:
            time_diff = datetime.now() - self.last_trade_times[signal.ts_code]
            if time_diff.total_seconds() < self.min_trade_interval:
                return False, f"交易间隔过短，需等待 {self.min_trade_interval - time_diff.total_seconds():.0f} 秒"
        
        # 检查单笔交易金额
        trade_amount = signal.price * signal.quantity
        if trade_amount > self.max_trade_amount:
            return False, f"单笔交易金额 {trade_amount:.2f} 超过限制 {self.max_trade_amount:.2f}"
        
        current_position = self.current_positions.get(signal.ts_code, 0)
        
        if signal.action == TradeAction.BUY:
            # 买入检查
            new_position = current_position + signal.quantity
            if new_position > self.max_position_per_stock:
                return False, f"买入后持仓 {new_position} 将超过单股票最大持仓限制 {self.max_position_per_stock}"
            
            # 检查持仓股票数量
            if current_position == 0:  # 新开仓
                current_stock_count = len([pos for pos in self.current_positions.values() if pos > 0])
                if current_stock_count >= self.max_total_positions:
                    return False, f"持仓股票数量 {current_stock_count} 已达到最大限制 {self.max_total_positions}"
            
            # 检查可用资金
            if trade_amount > self.available_funds:
                return False, f"可用资金 {self.available_funds:.2f} 不足，需要 {trade_amount:.2f}"
        
        elif signal.action == TradeAction.SELL:
            # 卖出检查
            if current_position < signal.quantity:
                return False, f"持仓数量 {current_position} 不足，无法卖出 {signal.quantity}"
        
        return True, ""
    
    def update_position(self, ts_code: str, action: TradeAction, quantity: int, price: float):
        """更新持仓信息"""
        current_position = self.current_positions.get(ts_code, 0)
        
        if action == TradeAction.BUY:
            self.current_positions[ts_code] = current_position + quantity
            self.available_funds -= price * quantity
        elif action == TradeAction.SELL:
            self.current_positions[ts_code] = max(0, current_position - quantity)
            self.available_funds += price * quantity
            
            # 如果完全卖出，从持仓中移除
            if self.current_positions[ts_code] == 0:
                del self.current_positions[ts_code]
        
        # 更新交易记录
        self.last_trade_times[ts_code] = datetime.now()
        self.daily_trade_count += 1
        
        logger.info(f"持仓更新: {ts_code} {action.value} {quantity}股 @ {price:.2f}, 当前持仓: {self.current_positions.get(ts_code, 0)}")
    
    def set_available_funds(self, funds: float):
        """设置可用资金"""
        self.available_funds = funds
    
    def _reset_daily_counter_if_needed(self):
        """如果需要，重置每日计数器"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_trade_count = 0
            self.last_reset_date = today
            logger.info("每日交易计数器已重置")
    
    def get_risk_status(self) -> Dict:
        """获取风险状态信息"""
        self._reset_daily_counter_if_needed()
        
        return {
            'current_positions': dict(self.current_positions),
            'total_position_count': len(self.current_positions),
            'daily_trade_count': self.daily_trade_count,
            'available_funds': self.available_funds,
            'max_position_per_stock': self.max_position_per_stock,
            'max_total_positions': self.max_total_positions,
            'max_daily_trades': self.max_daily_trades,
            'max_trade_amount': self.max_trade_amount
        }


class TradingScheduler:
    """交易调度器"""
    
    def __init__(self, trade_executor: Callable = None):
        self.trade_executor = trade_executor  # 实际执行交易的函数
        self.risk_manager = RiskManager()
        
        # 信号队列（优先级队列）
        self.signal_queue = queue.PriorityQueue()
        self.executing_signals: Dict[str, TradeSignal] = {}  # 正在执行的信号
        self.completed_signals: List[TradeSignal] = []  # 已完成的信号
        
        # 控制变量
        self.is_running = False
        self.scheduler_thread = None
        self.max_concurrent_trades = 3  # 最大并发交易数
        self.trade_timeout = 30  # 交易超时时间（秒）
        
        # 回调函数
        self.signal_callback = None
        self.trade_callback = None
        self.error_callback = None
        
        logger.info("交易调度器初始化完成")
    
    def set_callbacks(self, signal_callback: Callable = None,
                     trade_callback: Callable = None,
                     error_callback: Callable = None):
        """设置回调函数"""
        self.signal_callback = signal_callback
        self.trade_callback = trade_callback
        self.error_callback = error_callback
    
    def add_signal(self, ts_code: str, action: str, price: float, 
                   quantity: int = 0, priority: int = 1, strategy_type: str = "") -> bool:
        """添加交易信号"""
        try:
            trade_action = TradeAction(action.upper())
            signal = TradeSignal(ts_code, trade_action, price, quantity, priority, strategy_type)
            
            # 风险检查
            allowed, reason = self.risk_manager.check_trade_allowed(signal)
            if not allowed:
                logger.warning(f"交易信号被风险控制拒绝: {ts_code} {action} - {reason}")
                signal.status = "CANCELLED"
                signal.error_message = reason
                self.completed_signals.append(signal)
                
                if self.error_callback:
                    self.error_callback(f"交易被拒绝: {reason}")
                return False
            
            # 添加到队列
            self.signal_queue.put(signal)
            logger.info(f"交易信号已添加: {ts_code} {action} {quantity}股 @ {price:.2f}")
            
            if self.signal_callback:
                self.signal_callback(signal.to_dict())
            
            return True
            
        except Exception as e:
            logger.error(f"添加交易信号失败: {e}")
            if self.error_callback:
                self.error_callback(f"添加交易信号失败: {e}")
            return False
    
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("交易调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("交易调度器已停止")
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.is_running:
            try:
                # 清理超时的执行中信号
                self._cleanup_timeout_signals()
                
                # 检查是否可以执行新的交易
                if len(self.executing_signals) < self.max_concurrent_trades:
                    try:
                        # 获取下一个信号（阻塞1秒）
                        signal = self.signal_queue.get(timeout=1)
                        
                        # 再次进行风险检查（状态可能已变化）
                        allowed, reason = self.risk_manager.check_trade_allowed(signal)
                        if allowed:
                            self._execute_signal(signal)
                        else:
                            logger.warning(f"执行前风险检查失败: {signal.ts_code} - {reason}")
                            signal.status = "CANCELLED"
                            signal.error_message = reason
                            self.completed_signals.append(signal)
                        
                    except queue.Empty:
                        # 队列为空，继续循环
                        pass
                else:
                    # 达到最大并发数，等待
                    time.sleep(1)
                
            except Exception as e:
                logger.error(f"调度器循环出错: {e}")
                time.sleep(1)
    
    def _execute_signal(self, signal: TradeSignal):
        """执行交易信号"""
        signal.status = "EXECUTING"
        signal.scheduled_time = datetime.now()
        self.executing_signals[signal.ts_code] = signal
        
        def execute_trade():
            try:
                logger.info(f"开始执行交易: {signal.ts_code} {signal.action.value} {signal.quantity}股 @ {signal.price:.2f}")
                
                # 调用实际的交易执行函数
                if self.trade_executor:
                    success = self.trade_executor(
                        signal.action.value,
                        signal.ts_code,
                        signal.quantity,
                        signal.price
                    )
                    
                    if success:
                        signal.status = "COMPLETED"
                        signal.executed_time = datetime.now()
                        
                        # 更新风险管理器的持仓信息
                        self.risk_manager.update_position(
                            signal.ts_code, signal.action, signal.quantity, signal.price
                        )
                        
                        logger.info(f"交易执行成功: {signal.ts_code} {signal.action.value}")
                        
                        if self.trade_callback:
                            self.trade_callback(signal.to_dict())
                    else:
                        raise Exception("交易执行函数返回失败")
                else:
                    # 模拟执行（用于测试）
                    time.sleep(2)
                    signal.status = "COMPLETED"
                    signal.executed_time = datetime.now()
                    logger.info(f"模拟交易执行完成: {signal.ts_code} {signal.action.value}")
                
            except Exception as e:
                signal.status = "FAILED"
                signal.error_message = str(e)
                signal.retry_count += 1
                
                logger.error(f"交易执行失败: {signal.ts_code} {signal.action.value} - {e}")
                
                # 如果还有重试次数，重新加入队列
                if signal.retry_count < signal.max_retries:
                    signal.status = "PENDING"
                    signal.priority += 1  # 降低优先级
                    self.signal_queue.put(signal)
                    logger.info(f"交易将重试: {signal.ts_code} (第{signal.retry_count}次)")
                else:
                    logger.error(f"交易重试次数已用完: {signal.ts_code}")
                    if self.error_callback:
                        self.error_callback(f"交易失败: {signal.ts_code} - {e}")
            
            finally:
                # 从执行中列表移除
                if signal.ts_code in self.executing_signals:
                    del self.executing_signals[signal.ts_code]
                
                # 添加到完成列表
                self.completed_signals.append(signal)
                
                # 保持完成列表大小
                if len(self.completed_signals) > 1000:
                    self.completed_signals = self.completed_signals[-500:]
        
        # 在新线程中执行交易
        threading.Thread(target=execute_trade, daemon=True).start()
    
    def _cleanup_timeout_signals(self):
        """清理超时的执行中信号"""
        current_time = datetime.now()
        timeout_signals = []
        
        for ts_code, signal in self.executing_signals.items():
            if signal.scheduled_time:
                elapsed = (current_time - signal.scheduled_time).total_seconds()
                if elapsed > self.trade_timeout:
                    timeout_signals.append(ts_code)
        
        for ts_code in timeout_signals:
            signal = self.executing_signals[ts_code]
            signal.status = "FAILED"
            signal.error_message = f"交易超时 ({self.trade_timeout}秒)"
            
            del self.executing_signals[ts_code]
            self.completed_signals.append(signal)
            
            logger.warning(f"交易超时: {ts_code}")
            
            if self.error_callback:
                self.error_callback(f"交易超时: {ts_code}")
    
    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        return {
            'pending_signals': self.signal_queue.qsize(),
            'executing_signals': len(self.executing_signals),
            'completed_signals': len(self.completed_signals),
            'is_running': self.is_running,
            'max_concurrent_trades': self.max_concurrent_trades
        }
    
    def get_recent_trades(self, limit: int = 50) -> List[Dict]:
        """获取最近的交易记录"""
        recent_trades = sorted(
            self.completed_signals,
            key=lambda x: x.executed_time or x.created_time,
            reverse=True
        )[:limit]
        
        return [signal.to_dict() for signal in recent_trades]
    
    def update_risk_settings(self, **kwargs):
        """更新风险控制设置"""
        for key, value in kwargs.items():
            if hasattr(self.risk_manager, key):
                setattr(self.risk_manager, key, value)
                logger.info(f"风险控制参数已更新: {key} = {value}")
    
    def set_available_funds(self, funds: float):
        """设置可用资金"""
        self.risk_manager.set_available_funds(funds)
    
    def update_position(self, ts_code: str, position: int):
        """手动更新持仓信息"""
        self.risk_manager.current_positions[ts_code] = position
        if position == 0 and ts_code in self.risk_manager.current_positions:
            del self.risk_manager.current_positions[ts_code]
