# 梦百合实用交易策略 - 适用于股票看图软件
# 基于技术指标组合，目标实现稳定收益

# 策略参数
rsi_period = 14
rsi_oversold = 35
rsi_overbought = 65
ma_short = 5
ma_medium = 20
ma_long = 60
bb_period = 20
bb_std = 2.0
volume_period = 20
volume_multiplier = 1.2

# 交易参数
stop_loss_pct = 0.05    # 5%止损
take_profit_pct = 0.08  # 8%止盈
min_hold_days = 3       # 最小持仓天数

# 计算技术指标
rsi = RSI(data['close'], rsi_period)
ma_short_line = SMA(data['close'], ma_short)
ma_medium_line = SMA(data['close'], ma_medium)
ma_long_line = SMA(data['close'], ma_long)
upper, middle, lower = BOLL(data['close'], bb_period, bb_std)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)
vol_ma = SMA(data['vol'], volume_period)

# 计算价格动量
momentum_5 = data['close'].pct_change(5)

# 初始化信号数组
signals = [0] * len(data)

# 状态变量
position = 0
entry_price = 0
entry_date = 0

# 生成交易信号
for i in range(max(rsi_period, ma_long, bb_period, volume_period), len(data)):
    if (pd.notna(rsi.iloc[i]) and pd.notna(ma_short_line.iloc[i]) and 
        pd.notna(ma_medium_line.iloc[i]) and pd.notna(ma_long_line.iloc[i]) and
        pd.notna(lower.iloc[i]) and pd.notna(dif.iloc[i]) and
        pd.notna(vol_ma.iloc[i])):
        
        current_price = data['close'].iloc[i]
        prev_price = data['close'].iloc[i-1]
        
        # 止损止盈检查（如果有持仓）
        if position == 1 and entry_price > 0:
            # 检查最小持仓期
            if i - entry_date >= min_hold_days:
                # 止盈
                if current_price >= entry_price * (1 + take_profit_pct):
                    signals[i] = -1
                    position = 0
                    entry_price = 0
                    entry_date = 0
                    continue
                # 止损
                elif current_price <= entry_price * (1 - stop_loss_pct):
                    signals[i] = -1
                    position = 0
                    entry_price = 0
                    entry_date = 0
                    continue
        
        # 买入条件（无持仓时）
        if position == 0:
            buy_conditions = []
            
            # 1. RSI超卖反弹
            rsi_buy = (rsi.iloc[i] < rsi_oversold and 
                      rsi.iloc[i] > rsi.iloc[i-1])
            buy_conditions.append(rsi_buy)
            
            # 2. 价格突破短期均线
            ma_breakout = (current_price > ma_short_line.iloc[i] and 
                          prev_price <= ma_short_line.iloc[i-1])
            buy_conditions.append(ma_breakout)
            
            # 3. 布林带支撑
            bb_support = current_price <= lower.iloc[i] * 1.03
            buy_conditions.append(bb_support)
            
            # 4. MACD金叉
            macd_golden = (dif.iloc[i] > dea.iloc[i] and 
                          dif.iloc[i-1] <= dea.iloc[i-1])
            buy_conditions.append(macd_golden)
            
            # 5. 成交量放大
            volume_up = data['vol'].iloc[i] > vol_ma.iloc[i] * volume_multiplier
            buy_conditions.append(volume_up)
            
            # 6. 价格上涨
            price_up = current_price > prev_price
            buy_conditions.append(price_up)
            
            # 7. 中期趋势向上
            trend_up = ma_short_line.iloc[i] > ma_medium_line.iloc[i]
            buy_conditions.append(trend_up)
            
            # 8. 正向动量
            momentum_positive = momentum_5.iloc[i] > 0
            buy_conditions.append(momentum_positive)
            
            # 买入决策：8个条件中满足4个即可
            if sum(buy_conditions) >= 4:
                signals[i] = 1
                position = 1
                entry_price = current_price
                entry_date = i
        
        # 卖出条件（有持仓时）
        elif position == 1:
            sell_conditions = []
            
            # 1. RSI超买
            rsi_sell = rsi.iloc[i] > rsi_overbought
            sell_conditions.append(rsi_sell)
            
            # 2. 价格跌破短期均线
            ma_breakdown = (current_price < ma_short_line.iloc[i] and 
                           prev_price >= ma_short_line.iloc[i-1])
            sell_conditions.append(ma_breakdown)
            
            # 3. 布林带阻力
            bb_resistance = current_price >= upper.iloc[i] * 0.97
            sell_conditions.append(bb_resistance)
            
            # 4. MACD死叉
            macd_death = (dif.iloc[i] < dea.iloc[i] and 
                         dif.iloc[i-1] >= dea.iloc[i-1])
            sell_conditions.append(macd_death)
            
            # 5. 价格下跌
            price_down = current_price < prev_price
            sell_conditions.append(price_down)
            
            # 6. 趋势转弱
            trend_weak = ma_short_line.iloc[i] < ma_medium_line.iloc[i]
            sell_conditions.append(trend_weak)
            
            # 7. 负向动量
            momentum_negative = momentum_5.iloc[i] < -0.01
            sell_conditions.append(momentum_negative)
            
            # 卖出决策：7个条件中满足3个即可
            if sum(sell_conditions) >= 3:
                signals[i] = -1
                position = 0
                entry_price = 0
                entry_date = 0
