#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录成功提示功能
"""

import tkinter as tk
from tkinter import ttk
import time

def test_success_message():
    """测试成功提示窗口"""
    
    def show_success_message(parent_root, message):
        """显示成功提示信息"""
        try:
            # 创建成功提示窗口
            success_window = tk.Toplevel(parent_root)
            success_window.title("登录成功")
            success_window.geometry("400x150")
            success_window.resizable(False, False)
            
            # 设置窗口居中
            success_window.transient(parent_root)
            success_window.grab_set()
            
            # 计算居中位置
            success_window.update_idletasks()
            x = (success_window.winfo_screenwidth() // 2) - (400 // 2)
            y = (success_window.winfo_screenheight() // 2) - (150 // 2)
            success_window.geometry(f"400x150+{x}+{y}")
            
            # 成功图标和文字
            main_frame = tk.Frame(success_window, bg='white')
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # 成功图标（使用绿色的✓）
            icon_label = tk.Label(main_frame, text="✓", font=("Arial", 24, "bold"), 
                                 fg="green", bg='white')
            icon_label.pack(pady=(10, 5))
            
            # 成功消息
            message_label = tk.Label(main_frame, text=message, font=("Arial", 12), 
                                   fg="darkgreen", bg='white', wraplength=350)
            message_label.pack(pady=5)
            
            # 进度提示
            progress_label = tk.Label(main_frame, text="请稍候...", font=("Arial", 10), 
                                    fg="gray", bg='white')
            progress_label.pack(pady=(5, 10))
            
            # 自动关闭窗口（3秒后）
            def auto_close():
                try:
                    success_window.destroy()
                except:
                    pass
            
            success_window.after(3000, auto_close)
            
            print(f"显示成功提示: {message}")
            
        except Exception as e:
            print(f"显示成功提示失败: {str(e)}")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("测试成功提示")
    root.geometry("300x200")
    
    # 居中显示
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (300 // 2)
    y = (root.winfo_screenheight() // 2) - (200 // 2)
    root.geometry(f"300x200+{x}+{y}")
    
    # 创建测试按钮
    frame = tk.Frame(root, padx=20, pady=20)
    frame.pack(fill=tk.BOTH, expand=True)
    
    tk.Label(frame, text="测试成功提示功能", font=("Arial", 14, "bold")).pack(pady=10)
    
    # 测试登录成功提示
    def test_login_success():
        show_success_message(root, "登录成功！正在启动股票看图软件...")
    
    # 测试注册成功提示
    def test_register_success():
        show_success_message(root, "注册成功！正在启动股票看图软件...")
    
    tk.Button(frame, text="测试登录成功提示", command=test_login_success,
             bg='lightgreen', font=("Arial", 10)).pack(pady=5, fill=tk.X)
    
    tk.Button(frame, text="测试注册成功提示", command=test_register_success,
             bg='lightblue', font=("Arial", 10)).pack(pady=5, fill=tk.X)
    
    tk.Button(frame, text="退出测试", command=root.quit,
             bg='lightcoral', font=("Arial", 10)).pack(pady=10, fill=tk.X)
    
    print("成功提示测试窗口已打开")
    print("点击按钮测试不同的成功提示效果")
    
    root.mainloop()

if __name__ == "__main__":
    test_success_message()
