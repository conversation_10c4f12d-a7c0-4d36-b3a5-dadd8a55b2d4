import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List
import warnings
warnings.filterwarnings('ignore')

class BacktestAnalyzer:
    """回测结果分析器"""
    
    def __init__(self, results: Dict):
        self.results = results
        self.equity_curve = results.get('equity_curve', pd.DataFrame())
        self.trades = results.get('trades', pd.DataFrame())
        self.signal_data = results.get('signal_data', pd.DataFrame())
        
    def calculate_advanced_metrics(self) -> Dict:
        """计算高级风险指标"""
        if self.equity_curve.empty:
            return {}
        
        returns = self.equity_curve['daily_return'].dropna()
        
        # 计算各种风险指标
        metrics = {}
        
        # 基础指标
        metrics['总收益率'] = self.results.get('total_return', 0)
        metrics['年化收益率'] = self.results.get('annual_return', 0)
        metrics['波动率'] = self.results.get('volatility', 0)
        metrics['夏普比率'] = self.results.get('sharpe_ratio', 0)
        metrics['最大回撤'] = self.results.get('max_drawdown', 0)
        
        # 高级指标
        if len(returns) > 0:
            # 索提诺比率 (Sortino Ratio)
            downside_returns = returns[returns < 0]
            downside_std = downside_returns.std() * np.sqrt(252)
            if downside_std > 0:
                metrics['索提诺比率'] = (self.results.get('annual_return', 0) - 0.03) / downside_std
            else:
                metrics['索提诺比率'] = 0
            
            # 卡尔马比率 (Calmar Ratio)
            if self.results.get('max_drawdown', 0) < 0:
                metrics['卡尔马比率'] = self.results.get('annual_return', 0) / abs(self.results.get('max_drawdown', 0))
            else:
                metrics['卡尔马比率'] = 0
            
            # VaR (Value at Risk) 95%置信度
            metrics['VaR_95%'] = np.percentile(returns, 5)
            
            # CVaR (Conditional VaR)
            var_95 = metrics['VaR_95%']
            cvar_returns = returns[returns <= var_95]
            metrics['CVaR_95%'] = cvar_returns.mean() if len(cvar_returns) > 0 else 0
            
            # 偏度和峰度
            metrics['偏度'] = returns.skew()
            metrics['峰度'] = returns.kurtosis()
            
            # 最大连续亏损天数
            metrics['最大连续亏损天数'] = self._calculate_max_consecutive_losses(returns)
            
            # 盈亏比
            winning_trades = returns[returns > 0]
            losing_trades = returns[returns < 0]
            if len(losing_trades) > 0 and len(winning_trades) > 0:
                metrics['盈亏比'] = winning_trades.mean() / abs(losing_trades.mean())
            else:
                metrics['盈亏比'] = 0
        
        return metrics
    
    def _calculate_max_consecutive_losses(self, returns: pd.Series) -> int:
        """计算最大连续亏损天数"""
        consecutive_losses = 0
        max_consecutive_losses = 0
        
        for ret in returns:
            if ret < 0:
                consecutive_losses += 1
                max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
            else:
                consecutive_losses = 0
        
        return max_consecutive_losses
    
    def analyze_drawdowns(self) -> pd.DataFrame:
        """分析回撤详情"""
        if self.equity_curve.empty:
            return pd.DataFrame()
        
        equity = self.equity_curve['total_value']
        cummax = equity.cummax()
        drawdown = (equity - cummax) / cummax
        
        # 找到回撤期间
        drawdown_periods = []
        in_drawdown = False
        start_idx = None
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and not in_drawdown:
                # 开始回撤
                in_drawdown = True
                start_idx = i
            elif dd >= 0 and in_drawdown:
                # 结束回撤
                in_drawdown = False
                end_idx = i - 1
                
                if start_idx is not None:
                    period_drawdown = drawdown.iloc[start_idx:end_idx+1]
                    max_dd = period_drawdown.min()
                    duration = end_idx - start_idx + 1
                    
                    drawdown_periods.append({
                        '开始日期': self.equity_curve.iloc[start_idx]['date'],
                        '结束日期': self.equity_curve.iloc[end_idx]['date'],
                        '持续天数': duration,
                        '最大回撤': max_dd,
                        '开始净值': equity.iloc[start_idx],
                        '最低净值': equity.iloc[start_idx:end_idx+1].min(),
                        '恢复净值': equity.iloc[end_idx]
                    })
        
        return pd.DataFrame(drawdown_periods)
    
    def analyze_monthly_returns(self) -> pd.DataFrame:
        """分析月度收益率"""
        if self.equity_curve.empty:
            return pd.DataFrame()
        
        # 转换日期格式
        equity_df = self.equity_curve.copy()
        equity_df['date'] = pd.to_datetime(equity_df['date'])
        equity_df.set_index('date', inplace=True)
        
        # 计算月度收益率
        monthly_equity = equity_df['total_value'].resample('M').last()
        monthly_returns = monthly_equity.pct_change().dropna()
        
        # 创建月度收益率表格
        monthly_df = pd.DataFrame({
            '月份': monthly_returns.index.strftime('%Y-%m'),
            '月收益率': monthly_returns.values,
            '累计收益率': (monthly_equity / monthly_equity.iloc[0] - 1).values[1:]
        })
        
        return monthly_df
    
    def plot_comprehensive_analysis(self):
        """绘制综合分析图表"""
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
        fig = plt.figure(figsize=(16, 12))
        
        # 1. 资产净值和回撤
        ax1 = plt.subplot(3, 2, 1)
        equity_df = self.equity_curve
        ax1.plot(equity_df.index, equity_df['total_value'], label='资产净值', linewidth=2)
        ax1.axhline(y=self.results['initial_capital'], color='r', linestyle='--', alpha=0.7)
        ax1.set_title('资产净值曲线')
        ax1.set_ylabel('资产价值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 回撤曲线
        ax2 = plt.subplot(3, 2, 2)
        ax2.fill_between(equity_df.index, equity_df['drawdown'], 0, 
                        color='red', alpha=0.3, label='回撤')
        ax2.set_title('回撤曲线')
        ax2.set_ylabel('回撤比例')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 日收益率分布
        ax3 = plt.subplot(3, 2, 3)
        returns = equity_df['daily_return'].dropna()
        ax3.hist(returns, bins=50, alpha=0.7, color='blue', edgecolor='black')
        ax3.axvline(returns.mean(), color='red', linestyle='--', label=f'均值: {returns.mean():.4f}')
        ax3.set_title('日收益率分布')
        ax3.set_xlabel('日收益率')
        ax3.set_ylabel('频次')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 滚动夏普比率
        ax4 = plt.subplot(3, 2, 4)
        if len(returns) > 30:
            rolling_sharpe = returns.rolling(30).mean() / returns.rolling(30).std() * np.sqrt(252)
            ax4.plot(rolling_sharpe.index, rolling_sharpe, label='30日滚动夏普比率')
            ax4.axhline(y=0, color='r', linestyle='--', alpha=0.7)
            ax4.set_title('滚动夏普比率')
            ax4.set_ylabel('夏普比率')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        
        # 5. 月度收益率热力图
        ax5 = plt.subplot(3, 2, 5)
        monthly_df = self.analyze_monthly_returns()
        if not monthly_df.empty:
            # 创建年月矩阵
            monthly_df['年'] = pd.to_datetime(monthly_df['月份']).dt.year
            monthly_df['月'] = pd.to_datetime(monthly_df['月份']).dt.month
            
            pivot_table = monthly_df.pivot(index='年', columns='月', values='月收益率')
            sns.heatmap(pivot_table, annot=True, fmt='.2%', cmap='RdYlGn', center=0, ax=ax5)
            ax5.set_title('月度收益率热力图')
        
        # 6. 累计收益率对比
        ax6 = plt.subplot(3, 2, 6)
        cumulative_returns = (equity_df['total_value'] / self.results['initial_capital'] - 1) * 100
        ax6.plot(equity_df.index, cumulative_returns, label='策略累计收益率', linewidth=2)
        
        # 如果有基准数据，可以添加基准比较
        # benchmark_returns = ...  # 基准收益率
        # ax6.plot(equity_df.index, benchmark_returns, label='基准收益率', linewidth=2)
        
        ax6.set_title('累计收益率')
        ax6.set_ylabel('收益率 (%)')
        ax6.set_xlabel('时间')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def generate_report(self) -> str:
        """生成详细的回测报告"""
        advanced_metrics = self.calculate_advanced_metrics()
        drawdown_analysis = self.analyze_drawdowns()
        monthly_returns = self.analyze_monthly_returns()
        
        report = []
        report.append("=" * 80)
        report.append("详细回测分析报告")
        report.append("=" * 80)
        
        # 基础指标
        report.append("\n【基础指标】")
        report.append("-" * 40)
        for key, value in advanced_metrics.items():
            if isinstance(value, float):
                if '比率' in key or '收益率' in key or '回撤' in key or 'VaR' in key or 'CVaR' in key:
                    report.append(f"{key}: {value:.2%}")
                else:
                    report.append(f"{key}: {value:.4f}")
            else:
                report.append(f"{key}: {value}")
        
        # 回撤分析
        if not drawdown_analysis.empty:
            report.append("\n【主要回撤期间】")
            report.append("-" * 40)
            top_drawdowns = drawdown_analysis.nlargest(3, '最大回撤')
            for i, row in top_drawdowns.iterrows():
                report.append(f"回撤 {i+1}:")
                report.append(f"  时间: {row['开始日期']} 至 {row['结束日期']}")
                report.append(f"  持续: {row['持续天数']} 天")
                report.append(f"  最大回撤: {row['最大回撤']:.2%}")
                report.append("")
        
        # 交易统计
        if not self.trades.empty:
            report.append("\n【交易统计】")
            report.append("-" * 40)
            buy_trades = self.trades[self.trades['action'] == 'BUY']
            sell_trades = self.trades[self.trades['action'] == 'SELL']
            
            report.append(f"总交易次数: {len(buy_trades)}")
            report.append(f"平均持仓天数: {self._calculate_avg_holding_period():.1f} 天")
            
            if len(buy_trades) > 0:
                avg_trade_size = buy_trades['value'].mean()
                report.append(f"平均交易金额: ¥{avg_trade_size:,.2f}")
        
        return "\n".join(report)
    
    def _calculate_avg_holding_period(self) -> float:
        """计算平均持仓天数"""
        if self.trades.empty:
            return 0
        
        buy_trades = self.trades[self.trades['action'] == 'BUY']
        sell_trades = self.trades[self.trades['action'] == 'SELL']
        
        if len(buy_trades) == 0 or len(sell_trades) == 0:
            return 0
        
        holding_periods = []
        for i in range(min(len(buy_trades), len(sell_trades))):
            buy_date = pd.to_datetime(buy_trades.iloc[i]['date'])
            sell_date = pd.to_datetime(sell_trades.iloc[i]['date'])
            holding_period = (sell_date - buy_date).days
            holding_periods.append(holding_period)
        
        return np.mean(holding_periods) if holding_periods else 0
