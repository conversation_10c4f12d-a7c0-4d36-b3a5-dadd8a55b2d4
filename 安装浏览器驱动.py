#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键安装浏览器驱动脚本
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from 浏览器驱动管理 import BrowserDriverManager

class DriverInstallerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("浏览器驱动安装器")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        self.manager = BrowserDriverManager()
        self.create_widgets()
        
    def create_widgets(self):
        # 标题
        title_label = tk.Label(self.root, text="浏览器驱动安装器", 
                              font=("Arial", 16, "bold"), fg="blue")
        title_label.pack(pady=10)
        
        # 说明文本
        info_text = """
此工具将为您下载并安装以下浏览器驱动：
• Chrome 驱动 (chromedriver)
• Edge 驱动 (edgedriver) 
• Firefox 驱动 (geckodriver)

安装后，登录注册程序将不再需要联网下载驱动。
        """
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 10), wraplength=450)
        info_label.pack(pady=10)
        
        # 当前状态框架
        status_frame = ttk.LabelFrame(self.root, text="当前状态", padding=10)
        status_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.status_text = tk.Text(status_frame, height=8, width=50, 
                                  font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(status_frame, orient="vertical", 
                                 command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮框架
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=20)
        
        # 检查状态按钮
        self.check_btn = tk.Button(button_frame, text="检查当前状态", 
                                  command=self.check_status, bg="lightblue")
        self.check_btn.pack(side=tk.LEFT, padx=5)
        
        # 安装按钮
        self.install_btn = tk.Button(button_frame, text="一键安装驱动", 
                                    command=self.install_drivers, bg="lightgreen")
        self.install_btn.pack(side=tk.LEFT, padx=5)
        
        # 清空日志按钮
        self.clear_btn = tk.Button(button_frame, text="清空日志", 
                                  command=self.clear_log, bg="lightyellow")
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode='indeterminate')
        self.progress.pack(fill=tk.X, padx=20, pady=5)
        
        # 初始检查
        self.root.after(500, self.check_status)
        
    def log_message(self, message):
        """添加日志消息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def clear_log(self):
        """清空日志"""
        self.status_text.delete(1.0, tk.END)
        
    def check_status(self):
        """检查当前驱动状态"""
        self.log_message("=" * 40)
        self.log_message("🔍 检查浏览器驱动状态...")
        
        available = self.manager.get_available_drivers()
        
        drivers = ["chromedriver", "edgedriver", "geckodriver"]
        driver_names = {
            "chromedriver": "Chrome驱动",
            "edgedriver": "Edge驱动", 
            "geckodriver": "Firefox驱动"
        }
        
        for driver in drivers:
            name = driver_names[driver]
            if driver in available:
                path = self.manager.get_driver_path(driver)
                self.log_message(f"✅ {name}: 已安装 ({path})")
            else:
                self.log_message(f"❌ {name}: 未安装")
        
        if len(available) == len(drivers):
            self.log_message("🎉 所有驱动都已安装完成！")
            self.install_btn.config(text="重新安装驱动", bg="orange")
        elif len(available) > 0:
            self.log_message(f"⚠️  部分驱动已安装 ({len(available)}/{len(drivers)})")
            self.install_btn.config(text="补充安装驱动", bg="yellow")
        else:
            self.log_message("❌ 未安装任何驱动")
            self.install_btn.config(text="一键安装驱动", bg="lightgreen")
            
        self.log_message("=" * 40)
        
    def install_drivers(self):
        """安装驱动"""
        def install_thread():
            try:
                self.progress.start()
                self.install_btn.config(state=tk.DISABLED)
                
                self.log_message("🚀 开始安装浏览器驱动...")
                
                # 重定向manager的print输出到GUI
                original_print = print
                def gui_print(*args, **kwargs):
                    message = ' '.join(str(arg) for arg in args)
                    self.root.after(0, lambda: self.log_message(message))
                
                # 临时替换print函数
                import builtins
                builtins.print = gui_print
                
                try:
                    success = self.manager.download_all_drivers()
                    
                    if success:
                        self.root.after(0, lambda: self.log_message("🎉 所有驱动安装完成！"))
                        self.root.after(0, lambda: messagebox.showinfo("成功", "所有浏览器驱动安装完成！"))
                    else:
                        self.root.after(0, lambda: self.log_message("⚠️  部分驱动安装失败"))
                        self.root.after(0, lambda: messagebox.showwarning("警告", "部分驱动安装失败，请检查网络连接"))
                        
                finally:
                    # 恢复原始print函数
                    builtins.print = original_print
                    
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 安装过程出错: {str(e)}"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"安装失败: {str(e)}"))
            finally:
                self.root.after(0, lambda: self.progress.stop())
                self.root.after(0, lambda: self.install_btn.config(state=tk.NORMAL))
                self.root.after(1000, self.check_status)  # 1秒后重新检查状态
        
        # 在新线程中运行安装
        threading.Thread(target=install_thread, daemon=True).start()
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = DriverInstallerGUI()
        app.run()
    except Exception as e:
        print(f"启动失败: {str(e)}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
