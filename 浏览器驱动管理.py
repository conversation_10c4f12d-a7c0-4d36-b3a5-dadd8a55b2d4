#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器驱动管理模块
内置常用浏览器驱动，避免网络下载问题
"""

import os
import sys
import platform
import zipfile
import requests
import shutil
from pathlib import Path

class BrowserDriverManager:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.drivers_dir = self.base_dir / "drivers"
        self.drivers_dir.mkdir(exist_ok=True)
        
        # 系统信息
        self.system = platform.system().lower()
        self.architecture = platform.machine().lower()
        
        # 驱动版本配置
        self.driver_versions = {
            "chromedriver": "119.0.6045.105",  # 稳定版本
            "edgedriver": "119.0.2151.44",     # 稳定版本
            "geckodriver": "0.33.0"            # Firefox驱动
        }
        
        # 驱动下载URL
        self.download_urls = {
            "chromedriver": {
                "windows": "https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip",
                "linux": "https://chromedriver.storage.googleapis.com/{version}/chromedriver_linux64.zip",
                "darwin": "https://chromedriver.storage.googleapis.com/{version}/chromedriver_mac64.zip"
            },
            "edgedriver": {
                "windows": "https://msedgedriver.azureedge.net/{version}/edgedriver_win64.zip",
                "linux": "https://msedgedriver.azureedge.net/{version}/edgedriver_linux64.zip",
                "darwin": "https://msedgedriver.azureedge.net/{version}/edgedriver_mac64.zip"
            },
            "geckodriver": {
                "windows": "https://github.com/mozilla/geckodriver/releases/download/v{version}/geckodriver-v{version}-win64.zip",
                "linux": "https://github.com/mozilla/geckodriver/releases/download/v{version}/geckodriver-v{version}-linux64.tar.gz",
                "darwin": "https://github.com/mozilla/geckodriver/releases/download/v{version}/geckodriver-v{version}-macos.tar.gz"
            }
        }
    
    def get_driver_path(self, driver_name):
        """获取驱动路径"""
        exe_suffix = ".exe" if self.system == "windows" else ""
        driver_path = self.drivers_dir / f"{driver_name}{exe_suffix}"
        return str(driver_path)
    
    def is_driver_available(self, driver_name):
        """检查驱动是否可用"""
        driver_path = self.get_driver_path(driver_name)
        return os.path.exists(driver_path) and os.access(driver_path, os.X_OK)
    
    def download_driver(self, driver_name, force=False):
        """下载指定驱动"""
        if not force and self.is_driver_available(driver_name):
            print(f"✅ {driver_name} 已存在，跳过下载")
            return True
            
        try:
            print(f"📥 正在下载 {driver_name}...")
            
            # 获取下载URL
            version = self.driver_versions.get(driver_name)
            if not version:
                print(f"❌ 未知的驱动: {driver_name}")
                return False
                
            url_template = self.download_urls.get(driver_name, {}).get(self.system)
            if not url_template:
                print(f"❌ 不支持的系统: {self.system}")
                return False
                
            download_url = url_template.format(version=version)
            
            # 下载文件
            response = requests.get(download_url, timeout=30)
            response.raise_for_status()
            
            # 保存到临时文件
            temp_file = self.drivers_dir / f"temp_{driver_name}.zip"
            with open(temp_file, 'wb') as f:
                f.write(response.content)
            
            # 解压文件
            self._extract_driver(temp_file, driver_name)
            
            # 删除临时文件
            temp_file.unlink()
            
            print(f"✅ {driver_name} 下载完成")
            return True
            
        except Exception as e:
            print(f"❌ 下载 {driver_name} 失败: {str(e)}")
            return False
    
    def _extract_driver(self, archive_path, driver_name):
        """解压驱动文件"""
        exe_suffix = ".exe" if self.system == "windows" else ""
        target_path = self.drivers_dir / f"{driver_name}{exe_suffix}"
        
        if archive_path.suffix == '.zip':
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                # 查找驱动文件
                for file_info in zip_ref.filelist:
                    if driver_name in file_info.filename.lower() and file_info.filename.endswith(exe_suffix):
                        # 提取到目标位置
                        with zip_ref.open(file_info) as source, open(target_path, 'wb') as target:
                            shutil.copyfileobj(source, target)
                        break
        
        # 设置执行权限（Linux/Mac）
        if self.system != "windows":
            os.chmod(target_path, 0o755)
    
    def download_all_drivers(self):
        """下载所有驱动"""
        print("🚀 开始下载所有浏览器驱动...")
        
        drivers = ["chromedriver", "edgedriver", "geckodriver"]
        success_count = 0
        
        for driver in drivers:
            if self.download_driver(driver):
                success_count += 1
        
        print(f"📊 下载完成: {success_count}/{len(drivers)} 个驱动成功")
        return success_count == len(drivers)
    
    def get_available_drivers(self):
        """获取可用的驱动列表"""
        available = []
        for driver in ["chromedriver", "edgedriver", "geckodriver"]:
            if self.is_driver_available(driver):
                available.append(driver)
        return available
    
    def setup_drivers(self):
        """设置驱动（首次运行时调用）"""
        print("🔧 正在设置浏览器驱动...")
        
        available = self.get_available_drivers()
        if len(available) >= 1:
            print(f"✅ 发现可用驱动: {', '.join(available)}")
            return True
        
        print("📥 未发现可用驱动，开始下载...")
        return self.download_all_drivers()

def main():
    """主函数，用于测试和初始化"""
    manager = BrowserDriverManager()
    
    print("=" * 50)
    print("浏览器驱动管理器")
    print("=" * 50)
    
    # 检查现有驱动
    available = manager.get_available_drivers()
    if available:
        print(f"✅ 已有驱动: {', '.join(available)}")
    else:
        print("❌ 未发现任何驱动")
    
    # 询问是否下载
    if not available:
        choice = input("\n是否下载所有驱动? (y/n): ").lower()
        if choice == 'y':
            manager.download_all_drivers()
    
    print("=" * 50)

if __name__ == "__main__":
    main()
