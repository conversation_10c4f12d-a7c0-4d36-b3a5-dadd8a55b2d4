#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义策略模板和示例
"""

# 基础模板
BASIC_TEMPLATE = '''
# 自定义策略模板
# 可用变量：
# - data: 股票数据DataFrame，包含open, high, low, close, vol等列
# - parameters: 策略参数字典
# 可用函数：SMA, EMA, RSI, MACD, KDJ, BOLL, ATR, STOCH, CCI, WR, ROC, MOMENTUM, OBV, VWAP

# 在这里编写你的策略逻辑
# 最终需要生成signals列表，长度与data相同
# signals[i] = 1 表示买入信号
# signals[i] = -1 表示卖出信号  
# signals[i] = 0 表示无信号

signals = [0] * len(data)

# 示例：简单的双均线策略
short_ma = SMA(data['close'], 5)
long_ma = SMA(data['close'], 20)

for i in range(len(data)):
    if i > 0:
        # 短期均线上穿长期均线，买入
        if short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i-1] <= long_ma.iloc[i-1]:
            signals[i] = 1
        # 短期均线下穿长期均线，卖出
        elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i-1] >= long_ma.iloc[i-1]:
            signals[i] = -1
'''

# RSI策略示例
RSI_STRATEGY = '''
# RSI超买超卖策略
# 当RSI < 30时买入，RSI > 70时卖出

signals = [0] * len(data)

# 计算RSI指标
rsi = RSI(data['close'], 14)

for i in range(len(data)):
    if pd.notna(rsi.iloc[i]):
        # RSI超卖，买入信号
        if rsi.iloc[i] < 30:
            signals[i] = 1
        # RSI超买，卖出信号
        elif rsi.iloc[i] > 70:
            signals[i] = -1
'''

# 布林带策略示例
BOLLINGER_STRATEGY = '''
# 布林带突破策略
# 价格突破下轨买入，突破上轨卖出

signals = [0] * len(data)

# 计算布林带
upper, middle, lower = BOLL(data['close'], 20, 2)

for i in range(len(data)):
    if i > 0 and pd.notna(lower.iloc[i]):
        # 价格从下方突破下轨，买入
        if data['close'].iloc[i] > lower.iloc[i] and data['close'].iloc[i-1] <= lower.iloc[i-1]:
            signals[i] = 1
        # 价格从上方跌破上轨，卖出
        elif data['close'].iloc[i] < upper.iloc[i] and data['close'].iloc[i-1] >= upper.iloc[i-1]:
            signals[i] = -1
'''

# MACD策略示例
MACD_STRATEGY = '''
# MACD金叉死叉策略
# DIF上穿DEA买入，DIF下穿DEA卖出

signals = [0] * len(data)

# 计算MACD指标
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)

for i in range(1, len(data)):
    if pd.notna(dif.iloc[i]) and pd.notna(dea.iloc[i]):
        # 金叉：DIF上穿DEA
        if dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1]:
            signals[i] = 1
        # 死叉：DIF下穿DEA
        elif dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1]:
            signals[i] = -1
'''

# KDJ策略示例
KDJ_STRATEGY = '''
# KDJ超买超卖策略
# 在超卖区域K线上穿D线买入，在超买区域K线下穿D线卖出

signals = [0] * len(data)

# 计算KDJ指标
k, d, j = KDJ(data['high'], data['low'], data['close'], 9)

for i in range(1, len(data)):
    if pd.notna(k.iloc[i]) and pd.notna(d.iloc[i]):
        # 超卖区域金叉
        if k.iloc[i] > d.iloc[i] and k.iloc[i-1] <= d.iloc[i-1] and k.iloc[i] < 20:
            signals[i] = 1
        # 超买区域死叉
        elif k.iloc[i] < d.iloc[i] and k.iloc[i-1] >= d.iloc[i-1] and k.iloc[i] > 80:
            signals[i] = -1
'''

# 多指标组合策略示例
MULTI_INDICATOR_STRATEGY = '''
# 多指标组合策略
# 结合RSI、MACD、布林带的信号

signals = [0] * len(data)

# 计算各种指标
rsi = RSI(data['close'], 14)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)
upper, middle, lower = BOLL(data['close'], 20, 2)

for i in range(1, len(data)):
    if (pd.notna(rsi.iloc[i]) and pd.notna(dif.iloc[i]) and 
        pd.notna(dea.iloc[i]) and pd.notna(lower.iloc[i])):
        
        # 买入条件：RSI超卖 + MACD金叉 + 价格接近布林带下轨
        buy_condition = (
            rsi.iloc[i] < 35 and
            dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1] and
            data['close'].iloc[i] < lower.iloc[i] * 1.02
        )
        
        # 卖出条件：RSI超买 + MACD死叉 + 价格接近布林带上轨
        sell_condition = (
            rsi.iloc[i] > 65 and
            dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1] and
            data['close'].iloc[i] > upper.iloc[i] * 0.98
        )
        
        if buy_condition:
            signals[i] = 1
        elif sell_condition:
            signals[i] = -1
'''

# 动量策略示例
MOMENTUM_STRATEGY = '''
# 动量策略
# 基于价格动量和成交量的策略

signals = [0] * len(data)

# 计算动量指标
momentum = MOMENTUM(data['close'], 10)
roc = ROC(data['close'], 12)
volume_ma = SMA(data['vol'], 20)

for i in range(20, len(data)):
    if (pd.notna(momentum.iloc[i]) and pd.notna(roc.iloc[i]) and 
        pd.notna(volume_ma.iloc[i])):
        
        # 买入条件：正动量 + 价格上涨 + 成交量放大
        if (momentum.iloc[i] > 0 and 
            roc.iloc[i] > 2 and 
            data['vol'].iloc[i] > volume_ma.iloc[i] * 1.5):
            signals[i] = 1
        
        # 卖出条件：负动量 + 价格下跌
        elif momentum.iloc[i] < 0 and roc.iloc[i] < -2:
            signals[i] = -1
'''

# 均值回归策略示例
MEAN_REVERSION_STRATEGY = '''
# 均值回归策略
# 当价格偏离均线过多时进行反向操作

signals = [0] * len(data)

# 计算指标
ma20 = SMA(data['close'], 20)
std20 = data['close'].rolling(20).std()

for i in range(20, len(data)):
    if pd.notna(ma20.iloc[i]) and pd.notna(std20.iloc[i]):
        # 计算价格偏离度
        deviation = (data['close'].iloc[i] - ma20.iloc[i]) / std20.iloc[i]
        
        # 价格过度下跌，买入
        if deviation < -2:
            signals[i] = 1
        # 价格过度上涨，卖出
        elif deviation > 2:
            signals[i] = -1
'''

# 趋势跟踪策略示例
TREND_FOLLOWING_STRATEGY = '''
# 趋势跟踪策略
# 使用ATR和移动平均线确定趋势

signals = [0] * len(data)

# 计算指标
ma_short = SMA(data['close'], 10)
ma_long = SMA(data['close'], 30)
atr = ATR(data['high'], data['low'], data['close'], 14)

for i in range(30, len(data)):
    if (pd.notna(ma_short.iloc[i]) and pd.notna(ma_long.iloc[i]) and
        pd.notna(atr.iloc[i])):

        # 趋势向上且突破
        if (ma_short.iloc[i] > ma_long.iloc[i] and
            data['close'].iloc[i] > ma_short.iloc[i] + atr.iloc[i] * 0.5):
            signals[i] = 1

        # 趋势向下且跌破
        elif (ma_short.iloc[i] < ma_long.iloc[i] and
              data['close'].iloc[i] < ma_short.iloc[i] - atr.iloc[i] * 0.5):
            signals[i] = -1
'''

# 梦百合专用策略
MENGBAILE_STRATEGY = '''
# 梦百合实用交易策略
# 基于多技术指标组合，针对梦百合数据优化
# 目标：实现稳定的正收益

signals = [0] * len(data)

# 策略参数
rsi_period = 14
rsi_oversold = 40      # 放宽超卖线，更容易触发
rsi_overbought = 60    # 收紧超买线，更容易触发
ma_fast = 5
ma_slow = 20

# 计算技术指标
rsi = RSI(data['close'], rsi_period)
ma_fast_line = SMA(data['close'], ma_fast)
ma_slow_line = SMA(data['close'], ma_slow)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)

# 计算价格动量
momentum_5 = data['close'].pct_change(5)

# 生成交易信号
for i in range(max(rsi_period, ma_slow, 26), len(data)):
    if (pd.notna(rsi.iloc[i]) and pd.notna(ma_fast_line.iloc[i]) and
        pd.notna(ma_slow_line.iloc[i]) and pd.notna(dif.iloc[i])):

        current_price = data['close'].iloc[i]

        # 买入信号条件（任一满足即可）
        buy_conditions = [
            # 1. RSI超卖反弹
            rsi.iloc[i] < rsi_oversold and rsi.iloc[i] > rsi.iloc[i-1],

            # 2. 快线上穿慢线
            (ma_fast_line.iloc[i] > ma_slow_line.iloc[i] and
             ma_fast_line.iloc[i-1] <= ma_slow_line.iloc[i-1]),

            # 3. MACD金叉
            (dif.iloc[i] > dea.iloc[i] and
             dif.iloc[i-1] <= dea.iloc[i-1]),

            # 4. 价格突破快线
            (current_price > ma_fast_line.iloc[i] and
             data['close'].iloc[i-1] <= ma_fast_line.iloc[i-1]),

            # 5. 价格上涨且趋势向上
            (current_price > data['close'].iloc[i-1] and
             ma_fast_line.iloc[i] > ma_slow_line.iloc[i]),

            # 6. 正向动量
            pd.notna(momentum_5.iloc[i]) and momentum_5.iloc[i] > 0
        ]

        # 卖出信号条件（任一满足即可）
        sell_conditions = [
            # 1. RSI超买
            rsi.iloc[i] > rsi_overbought,

            # 2. 快线下穿慢线
            (ma_fast_line.iloc[i] < ma_slow_line.iloc[i] and
             ma_fast_line.iloc[i-1] >= ma_slow_line.iloc[i-1]),

            # 3. MACD死叉
            (dif.iloc[i] < dea.iloc[i] and
             dif.iloc[i-1] >= dea.iloc[i-1]),

            # 4. 价格跌破快线
            (current_price < ma_fast_line.iloc[i] and
             data['close'].iloc[i-1] >= ma_fast_line.iloc[i-1]),

            # 5. 价格下跌且趋势转弱
            (current_price < data['close'].iloc[i-1] and
             ma_fast_line.iloc[i] < ma_slow_line.iloc[i]),

            # 6. 负向动量
            pd.notna(momentum_5.iloc[i]) and momentum_5.iloc[i] < -0.01
        ]

        # 生成信号
        if any(buy_conditions):
            signals[i] = 1
        elif any(sell_conditions):
            signals[i] = -1
'''

# 缠论高收益策略
CHANLUN_HIGH_YIELD_STRATEGY = '''
# 缠论高收益策略
# 基于波段结构、中枢、支撑阻力位的精准交易策略

signals = [0] * len(data)

# 策略参数
fenxing_strength = 2.0
bi_min_bars = 5
volume_confirm_ratio = 1.5

# 计算技术指标
rsi = RSI(data['close'], 14)
ma_short = SMA(data['close'], 5)
ma_long = SMA(data['close'], 20)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)

# 识别关键价位
def find_key_levels(data, window=20):
    highs = []
    lows = []
    for i in range(window, len(data) - window):
        if data['high'].iloc[i] == data['high'].iloc[i-window:i+window+1].max():
            highs.append((i, data['high'].iloc[i]))
        if data['low'].iloc[i] == data['low'].iloc[i-window:i+window+1].min():
            lows.append((i, data['low'].iloc[i]))
    return highs, lows

resistance_levels, support_levels = find_key_levels(data)

# 生成交易信号
position = 0
entry_price = 0
highest_price = 0

for i in range(50, len(data)):
    current_price = data['close'].iloc[i]

    # 买入条件
    if position == 0:
        buy_conditions = []

        # 1. RSI超卖反弹
        if rsi.iloc[i] < 35 and rsi.iloc[i] > rsi.iloc[i-1]:
            buy_conditions.append(True)

        # 2. 均线支撑
        if current_price > ma_short.iloc[i] and ma_short.iloc[i] > ma_long.iloc[i]:
            buy_conditions.append(True)

        # 3. MACD金叉
        if dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1]:
            buy_conditions.append(True)

        # 4. 支撑位买入
        for _, support_price in support_levels[-5:]:
            if abs(current_price - support_price) / support_price < 0.02:
                buy_conditions.append(True)
                break

        # 5. 成交量确认
        if 'vol' in data.columns:
            recent_vol = data['vol'].iloc[max(0, i-5):i+1].mean()
            avg_vol = data['vol'].iloc[max(0, i-20):i].mean()
            if recent_vol > avg_vol * volume_confirm_ratio:
                buy_conditions.append(True)

        # 买入决策
        if len(buy_conditions) >= 3:
            signals[i] = 1
            position = 1
            entry_price = current_price
            highest_price = current_price

    # 卖出条件
    elif position == 1:
        # 更新最高价
        highest_price = max(highest_price, current_price)

        sell_conditions = []

        # 1. 止损止盈
        if current_price <= entry_price * 0.97:  # 3%止损
            signals[i] = -1
            position = 0
            continue
        elif current_price >= entry_price * 1.08:  # 8%止盈
            signals[i] = -1
            position = 0
            continue
        elif current_price <= highest_price * 0.98:  # 2%移动止损
            signals[i] = -1
            position = 0
            continue

        # 2. RSI超买
        if rsi.iloc[i] > 65:
            sell_conditions.append(True)

        # 3. 均线压力
        if current_price < ma_short.iloc[i] or ma_short.iloc[i] < ma_long.iloc[i]:
            sell_conditions.append(True)

        # 4. MACD死叉
        if dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1]:
            sell_conditions.append(True)

        # 5. 阻力位卖出
        for _, resistance_price in resistance_levels[-5:]:
            if abs(current_price - resistance_price) / resistance_price < 0.02:
                sell_conditions.append(True)
                break

        # 卖出决策
        if len(sell_conditions) >= 2:
            signals[i] = -1
            position = 0
'''

# 策略模板字典
STRATEGY_TEMPLATES = {
    "基础模板": BASIC_TEMPLATE,
    "RSI策略": RSI_STRATEGY,
    "布林带策略": BOLLINGER_STRATEGY,
    "MACD策略": MACD_STRATEGY,
    "KDJ策略": KDJ_STRATEGY,
    "多指标组合": MULTI_INDICATOR_STRATEGY,
    "动量策略": MOMENTUM_STRATEGY,
    "均值回归策略": MEAN_REVERSION_STRATEGY,
    "趋势跟踪策略": TREND_FOLLOWING_STRATEGY,
    "梦百合策略": MENGBAILE_STRATEGY,
    "缠论高收益策略": CHANLUN_HIGH_YIELD_STRATEGY
}

def get_template(name: str) -> str:
    """获取策略模板"""
    return STRATEGY_TEMPLATES.get(name, BASIC_TEMPLATE)

def get_template_names() -> list:
    """获取所有模板名称"""
    return list(STRATEGY_TEMPLATES.keys())

# 策略编写指南
STRATEGY_GUIDE = '''
# 自定义策略编写指南

## 可用变量
- data: 股票数据DataFrame，包含以下列：
  * open: 开盘价
  * high: 最高价  
  * low: 最低价
  * close: 收盘价
  * vol: 成交量
  * trade_date: 交易日期

- parameters: 策略参数字典（可在界面中设置）

## 可用技术指标函数
- SMA(data, period): 简单移动平均线
- EMA(data, period): 指数移动平均线
- RSI(data, period=14): 相对强弱指标
- MACD(data, fast=12, slow=26, signal=9): MACD指标，返回(DIF, DEA, MACD)
- KDJ(high, low, close, period=9): KDJ指标，返回(K, D, J)
- BOLL(data, period=20, std_dev=2): 布林带，返回(上轨, 中轨, 下轨)
- ATR(high, low, close, period=14): 平均真实波幅
- STOCH(high, low, close, k_period=14, d_period=3): 随机指标，返回(K, D)
- CCI(high, low, close, period=20): 顺势指标
- WR(high, low, close, period=14): 威廉指标
- ROC(data, period=12): 变动率指标
- MOMENTUM(data, period=10): 动量指标
- OBV(close, volume): 能量潮指标
- VWAP(high, low, close, volume): 成交量加权平均价

## 信号生成规则
策略必须生成signals列表，长度与data相同：
- signals[i] = 1: 买入信号
- signals[i] = -1: 卖出信号
- signals[i] = 0: 无信号（持有当前状态）

## 注意事项
1. 使用pd.notna()检查数据有效性
2. 注意数组边界，避免索引越界
3. 技术指标通常需要一定的数据长度才能计算
4. 可以使用parameters字典传递策略参数
5. 策略代码会在安全环境中执行，部分功能可能受限

## 示例代码结构
```python
# 初始化信号列表
signals = [0] * len(data)

# 计算技术指标
indicator = SMA(data['close'], 20)

# 生成交易信号
for i in range(len(data)):
    if 满足买入条件:
        signals[i] = 1
    elif 满足卖出条件:
        signals[i] = -1
```
'''
