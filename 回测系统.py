import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class Position:
    """持仓信息类"""
    def __init__(self, symbol: str, quantity: int, entry_price: float, entry_date: str):
        self.symbol = symbol
        self.quantity = quantity
        self.entry_price = entry_price
        self.entry_date = entry_date
        self.current_price = entry_price
        
    def update_price(self, price: float):
        """更新当前价格"""
        self.current_price = price
        
    def get_unrealized_pnl(self) -> float:
        """获取未实现盈亏"""
        return (self.current_price - self.entry_price) * self.quantity
        
    def get_value(self) -> float:
        """获取持仓市值"""
        return self.current_price * self.quantity

class Trade:
    """交易记录类"""
    def __init__(self, symbol: str, action: str, quantity: int, price: float, 
                 date: str, commission: float = 0.0):
        self.symbol = symbol
        self.action = action  # 'BUY' or 'SELL'
        self.quantity = quantity
        self.price = price
        self.date = date
        self.commission = commission
        self.value = quantity * price
        
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'symbol': self.symbol,
            'action': self.action,
            'quantity': self.quantity,
            'price': self.price,
            'date': self.date,
            'commission': self.commission,
            'value': self.value
        }

class Portfolio:
    """投资组合管理类"""
    def __init__(self, initial_capital: float = 100000.0, commission_rate: float = 0.001):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.commission_rate = commission_rate
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.equity_curve: List[Dict] = []
        
    def buy(self, symbol: str, quantity: int, price: float, date: str) -> bool:
        """买入股票"""
        total_cost = quantity * price
        commission = total_cost * self.commission_rate
        
        if self.cash >= total_cost + commission:
            self.cash -= (total_cost + commission)
            
            if symbol in self.positions:
                # 加仓
                old_pos = self.positions[symbol]
                new_quantity = old_pos.quantity + quantity
                new_avg_price = ((old_pos.quantity * old_pos.entry_price) + 
                               (quantity * price)) / new_quantity
                self.positions[symbol] = Position(symbol, new_quantity, new_avg_price, date)
            else:
                # 新建仓位
                self.positions[symbol] = Position(symbol, quantity, price, date)
            
            # 记录交易
            trade = Trade(symbol, 'BUY', quantity, price, date, commission)
            self.trades.append(trade)
            return True
        return False
    
    def sell(self, symbol: str, quantity: int, price: float, date: str) -> bool:
        """卖出股票"""
        if symbol in self.positions and self.positions[symbol].quantity >= quantity:
            total_value = quantity * price
            commission = total_value * self.commission_rate
            
            self.cash += (total_value - commission)
            
            # 更新持仓
            self.positions[symbol].quantity -= quantity
            if self.positions[symbol].quantity == 0:
                del self.positions[symbol]
            
            # 记录交易
            trade = Trade(symbol, 'SELL', quantity, price, date, commission)
            self.trades.append(trade)
            return True
        return False
    
    def update_positions(self, symbol: str, price: float):
        """更新持仓价格"""
        if symbol in self.positions:
            self.positions[symbol].update_price(price)
    
    def get_total_value(self) -> float:
        """获取总资产价值"""
        positions_value = sum(pos.get_value() for pos in self.positions.values())
        return self.cash + positions_value
    
    def record_equity(self, date: str):
        """记录资产净值"""
        total_value = self.get_total_value()
        self.equity_curve.append({
            'date': date,
            'total_value': total_value,
            'cash': self.cash,
            'positions_value': total_value - self.cash
        })

class Strategy(ABC):
    """交易策略基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.parameters = {}
    
    @abstractmethod
    def initialize(self, data: pd.DataFrame):
        """初始化策略"""
        pass
    
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        pass
    
    def set_parameters(self, **kwargs):
        """设置策略参数"""
        self.parameters.update(kwargs)

class MACDStrategy(Strategy):
    """MACD策略"""
    
    def __init__(self):
        super().__init__("MACD策略")
        self.parameters = {
            'fast_period': 12,
            'slow_period': 26,
            'signal_period': 9
        }
    
    def initialize(self, data: pd.DataFrame):
        """初始化MACD指标"""
        pass
    
    def calculate_macd(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MACD指标"""
        fast_period = self.parameters['fast_period']
        slow_period = self.parameters['slow_period']
        signal_period = self.parameters['signal_period']
        
        # 计算EMA
        ema_fast = data['close'].ewm(span=fast_period).mean()
        ema_slow = data['close'].ewm(span=slow_period).mean()
        
        # 计算MACD线
        data['dif'] = ema_fast - ema_slow
        data['dea'] = data['dif'].ewm(span=signal_period).mean()
        data['macd'] = 2 * (data['dif'] - data['dea'])
        
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成MACD交易信号"""
        data = self.calculate_macd(data)
        
        # 初始化信号列
        data['signal'] = 0
        data['position'] = 0
        
        # MACD金叉死叉信号
        for i in range(1, len(data)):
            # 金叉：DIF上穿DEA，买入信号
            if (data.iloc[i]['dif'] > data.iloc[i]['dea'] and 
                data.iloc[i-1]['dif'] <= data.iloc[i-1]['dea']):
                data.iloc[i, data.columns.get_loc('signal')] = 1
            
            # 死叉：DIF下穿DEA，卖出信号
            elif (data.iloc[i]['dif'] < data.iloc[i]['dea'] and 
                  data.iloc[i-1]['dif'] >= data.iloc[i-1]['dea']):
                data.iloc[i, data.columns.get_loc('signal')] = -1
        
        # 计算持仓状态
        position = 0
        for i in range(len(data)):
            if data.iloc[i]['signal'] == 1:
                position = 1
            elif data.iloc[i]['signal'] == -1:
                position = 0
            data.iloc[i, data.columns.get_loc('position')] = position
        
        return data

class KDJStrategy(Strategy):
    """KDJ策略"""

    def __init__(self):
        super().__init__("KDJ策略")
        self.parameters = {
            'k_period': 9,
            'k_smooth': 3,
            'd_smooth': 3,
            'oversold': 20,
            'overbought': 80
        }

    def initialize(self, data: pd.DataFrame):
        """初始化KDJ指标"""
        pass

    def calculate_kdj(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算KDJ指标"""
        k_period = self.parameters['k_period']

        # 计算RSV
        low_list = data['low'].rolling(k_period, min_periods=k_period).min()
        low_list.fillna(value=data['low'].expanding().min(), inplace=True)
        high_list = data['high'].rolling(k_period, min_periods=k_period).max()
        high_list.fillna(value=data['high'].expanding().max(), inplace=True)

        rsv = (data['close'] - low_list) / (high_list - low_list) * 100

        # 计算K、D、J值
        data['k'] = rsv.ewm(com=2).mean()
        data['d'] = data['k'].ewm(com=2).mean()
        data['j'] = 3 * data['k'] - 2 * data['d']

        return data

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成KDJ交易信号"""
        data = self.calculate_kdj(data)

        # 初始化信号列
        data['signal'] = 0
        data['position'] = 0

        oversold = self.parameters['oversold']
        overbought = self.parameters['overbought']

        # KDJ超买超卖信号
        for i in range(1, len(data)):
            # 超卖区域金叉，买入信号
            if (data.iloc[i]['k'] > data.iloc[i]['d'] and
                data.iloc[i-1]['k'] <= data.iloc[i-1]['d'] and
                data.iloc[i]['k'] < oversold):
                data.iloc[i, data.columns.get_loc('signal')] = 1

            # 超买区域死叉，卖出信号
            elif (data.iloc[i]['k'] < data.iloc[i]['d'] and
                  data.iloc[i-1]['k'] >= data.iloc[i-1]['d'] and
                  data.iloc[i]['k'] > overbought):
                data.iloc[i, data.columns.get_loc('signal')] = -1

        # 计算持仓状态
        position = 0
        for i in range(len(data)):
            if data.iloc[i]['signal'] == 1:
                position = 1
            elif data.iloc[i]['signal'] == -1:
                position = 0
            data.iloc[i, data.columns.get_loc('position')] = position

        return data

class CustomStrategy(Strategy):
    """自定义策略类"""

    def __init__(self, name: str = "自定义策略", code: str = ""):
        super().__init__(name)
        self.code = code
        # 移除实例级别的全局和局部变量，每次执行时重新创建

    def set_code(self, code: str):
        """设置策略代码"""
        self.code = code

    def initialize(self, data: pd.DataFrame):
        """初始化策略"""
        # 不在这里初始化执行环境，而是在generate_signals中每次重新创建
        pass

    def _import_indicator(self, indicator_name: str):
        """动态导入技术指标函数"""
        try:
            from 技术指标库 import TechnicalIndicators
            return getattr(TechnicalIndicators, indicator_name)
        except ImportError:
            # 如果导入失败，返回一个占位函数
            def placeholder(*args, **kwargs):
                raise ImportError(f"技术指标 {indicator_name} 不可用")
            return placeholder

    def _create_execution_environment(self, data: pd.DataFrame):
        """为每次执行创建独立的环境"""
        # 每次都创建全新的执行环境，避免状态污染
        user_globals = {
            'pd': pd,
            'np': np,
            'data': data.copy(),  # 确保数据是独立副本
            'parameters': self.parameters.copy(),  # 参数也使用副本
            # 导入技术指标函数
            'SMA': self._import_indicator('SMA'),
            'EMA': self._import_indicator('EMA'),
            'RSI': self._import_indicator('RSI'),
            'MACD': self._import_indicator('MACD'),
            'KDJ': self._import_indicator('KDJ'),
            'BOLL': self._import_indicator('BOLL'),
            'ATR': self._import_indicator('ATR'),
            'STOCH': self._import_indicator('STOCH'),
            'CCI': self._import_indicator('CCI'),
            'WR': self._import_indicator('WR'),
            'ROC': self._import_indicator('ROC'),
            'MOMENTUM': self._import_indicator('MOMENTUM'),
            'OBV': self._import_indicator('OBV'),
            'VWAP': self._import_indicator('VWAP'),
        }

        # 每次都创建全新的局部变量字典
        user_locals = {}

        return user_globals, user_locals

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        if not self.code.strip():
            # 如果没有代码，返回空信号
            data['signal'] = 0
            data['position'] = 0
            return data

        try:
            # 创建独立的数据副本，避免修改原始数据
            data_copy = data.copy()

            # 为每次执行创建独立的环境
            user_globals, user_locals = self._create_execution_environment(data_copy)

            # 初始化信号列
            data_copy['signal'] = 0
            data_copy['position'] = 0

            # 执行用户代码
            exec(self.code, user_globals, user_locals)

            # 获取用户生成的信号
            if 'signals' in user_locals:
                user_signals = user_locals['signals']
                if hasattr(user_signals, '__len__') and len(user_signals) == len(data_copy):
                    data_copy['signal'] = user_signals

            # 计算持仓状态
            position = 0
            for i in range(len(data_copy)):
                if data_copy.iloc[i]['signal'] == 1:
                    position = 1
                elif data_copy.iloc[i]['signal'] == -1:
                    position = 0
                data_copy.iloc[i, data_copy.columns.get_loc('position')] = position

            return data_copy

        except Exception as e:
            print(f"执行自定义策略代码时出错: {str(e)}")
            # 返回空信号的数据副本
            data_copy = data.copy()
            data_copy['signal'] = 0
            data_copy['position'] = 0
            return data_copy

class BacktestEngine:
    """回测引擎"""

    def __init__(self, initial_capital: float = 100000.0, commission_rate: float = 0.001):
        self.portfolio = Portfolio(initial_capital, commission_rate)
        self.strategy = None
        self.data = None
        self.results = {}

    def set_strategy(self, strategy: Strategy):
        """设置交易策略"""
        self.strategy = strategy

    def load_data(self, data: pd.DataFrame):
        """加载股票数据"""
        self.data = data.copy()
        # 确保数据按日期排序
        if 'trade_date' in self.data.columns:
            self.data = self.data.sort_values('trade_date')
        elif 'date' in self.data.columns:
            self.data = self.data.sort_values('date')

    def run_backtest(self, symbol: str = "STOCK") -> Dict:
        """运行回测"""
        if self.strategy is None or self.data is None:
            raise ValueError("策略和数据必须先设置")

        # 初始化策略
        self.strategy.initialize(self.data)

        # 生成交易信号
        signal_data = self.strategy.generate_signals(self.data.copy())

        # 执行交易
        for i, row in signal_data.iterrows():
            date = str(row.get('trade_date', row.get('date', i)))
            price = row['close']
            signal = row['signal']

            # 更新持仓价格
            self.portfolio.update_positions(symbol, price)

            # 执行交易
            if signal == 1:  # 买入信号
                # 计算可买入股数（使用90%的现金）
                available_cash = self.portfolio.cash * 0.9
                quantity = int(available_cash / price / 100) * 100  # 按手买入
                if quantity > 0:
                    self.portfolio.buy(symbol, quantity, price, date)

            elif signal == -1:  # 卖出信号
                # 卖出所有持仓
                if symbol in self.portfolio.positions:
                    quantity = self.portfolio.positions[symbol].quantity
                    self.portfolio.sell(symbol, quantity, price, date)

            # 记录资产净值
            self.portfolio.record_equity(date)

        # 计算回测结果
        self.results = self._calculate_results(signal_data)
        return self.results

    def _calculate_results(self, signal_data: pd.DataFrame) -> Dict:
        """计算回测结果统计"""
        if not self.portfolio.equity_curve:
            return {}

        # 转换为DataFrame
        equity_df = pd.DataFrame(self.portfolio.equity_curve)
        trades_df = pd.DataFrame([trade.to_dict() for trade in self.portfolio.trades])

        # 基本统计
        initial_value = self.portfolio.initial_capital
        final_value = equity_df['total_value'].iloc[-1]
        total_return = (final_value - initial_value) / initial_value

        # 计算日收益率
        equity_df['daily_return'] = equity_df['total_value'].pct_change()

        # 年化收益率
        trading_days = len(equity_df)
        annual_return = (final_value / initial_value) ** (252 / trading_days) - 1

        # 波动率
        volatility = equity_df['daily_return'].std() * np.sqrt(252)

        # 夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0

        # 最大回撤
        equity_df['cummax'] = equity_df['total_value'].cummax()
        equity_df['drawdown'] = (equity_df['total_value'] - equity_df['cummax']) / equity_df['cummax']
        max_drawdown = equity_df['drawdown'].min()

        # 交易统计
        if not trades_df.empty:
            buy_trades = trades_df[trades_df['action'] == 'BUY']
            sell_trades = trades_df[trades_df['action'] == 'SELL']
            total_trades = len(buy_trades)

            # 计算每笔交易的盈亏
            trade_returns = []
            if len(buy_trades) > 0 and len(sell_trades) > 0:
                for i in range(min(len(buy_trades), len(sell_trades))):
                    buy_price = buy_trades.iloc[i]['price']
                    sell_price = sell_trades.iloc[i]['price']
                    trade_return = (sell_price - buy_price) / buy_price
                    trade_returns.append(trade_return)

            win_trades = len([r for r in trade_returns if r > 0])
            win_rate = win_trades / len(trade_returns) if trade_returns else 0
        else:
            total_trades = 0
            win_rate = 0

        results = {
            'initial_capital': initial_value,
            'final_value': final_value,
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'equity_curve': equity_df,
            'trades': trades_df,
            'signal_data': signal_data
        }

        return results

    def plot_results(self):
        """绘制回测结果图表"""
        if not self.results:
            print("请先运行回测")
            return

        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(3, 1, figsize=(12, 10))

        # 资产净值曲线
        equity_df = self.results['equity_curve']
        signal_data = self.results['signal_data']

        axes[0].plot(equity_df.index, equity_df['total_value'], label='资产净值', linewidth=2)
        axes[0].axhline(y=self.portfolio.initial_capital, color='r', linestyle='--', alpha=0.7, label='初始资金')
        axes[0].set_title('资产净值曲线')
        axes[0].set_ylabel('资产价值')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # 股价和交易信号
        axes[1].plot(signal_data.index, signal_data['close'], label='收盘价', linewidth=1)

        # 标记买入卖出点
        buy_signals = signal_data[signal_data['signal'] == 1]
        sell_signals = signal_data[signal_data['signal'] == -1]

        axes[1].scatter(buy_signals.index, buy_signals['close'],
                       color='red', marker='^', s=100, label='买入信号', zorder=5)
        axes[1].scatter(sell_signals.index, sell_signals['close'],
                       color='green', marker='v', s=100, label='卖出信号', zorder=5)

        axes[1].set_title('股价与交易信号')
        axes[1].set_ylabel('股价')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # 回撤曲线
        axes[2].fill_between(equity_df.index, equity_df['drawdown'], 0,
                           color='red', alpha=0.3, label='回撤')
        axes[2].set_title('回撤曲线')
        axes[2].set_ylabel('回撤比例')
        axes[2].set_xlabel('时间')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def print_summary(self):
        """打印回测结果摘要"""
        if not self.results:
            print("请先运行回测")
            return

        print("=" * 50)
        print("回测结果摘要")
        print("=" * 50)
        print(f"策略名称: {self.strategy.name}")
        print(f"初始资金: ¥{self.results['initial_capital']:,.2f}")
        print(f"最终资产: ¥{self.results['final_value']:,.2f}")
        print(f"总收益率: {self.results['total_return']:.2%}")
        print(f"年化收益率: {self.results['annual_return']:.2%}")
        print(f"波动率: {self.results['volatility']:.2%}")
        print(f"夏普比率: {self.results['sharpe_ratio']:.3f}")
        print(f"最大回撤: {self.results['max_drawdown']:.2%}")
        print(f"交易次数: {self.results['total_trades']}")
        print(f"胜率: {self.results['win_rate']:.2%}")
        print("=" * 50)
