@echo off
chcp 65001 >nul
echo ====================================
echo 股票看图软件 - 快速打包脚本
echo ====================================
echo.

echo 正在检查环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✓ Python环境正常

echo.
echo 正在安装PyInstaller...
pip install pyinstaller

echo.
echo 正在创建打包配置...
echo # -*- mode: python ; coding: utf-8 -*- > 股票看图软件_增强版.spec
echo. >> 股票看图软件_增强版.spec
echo block_cipher = None >> 股票看图软件_增强版.spec
echo. >> 股票看图软件_增强版.spec
echo added_files = [ >> 股票看图软件_增强版.spec
echo     ('回测系统.py', '.'), >> 股票看图软件_增强版.spec
echo     ('回测分析.py', '.'), >> 股票看图软件_增强版.spec
echo     ('策略模板.py', '.'), >> 股票看图软件_增强版.spec
echo     ('多股票回测系统.py', '.'), >> 股票看图软件_增强版.spec
echo     ('技术指标库.py', '.'), >> 股票看图软件_增强版.spec
echo     ('策略示例', '策略示例'), >> 股票看图软件_增强版.spec
echo ] >> 股票看图软件_增强版.spec
echo. >> 股票看图软件_增强版.spec
echo a = Analysis( >> 股票看图软件_增强版.spec
echo     ['股票看图软件_增强版.py'], >> 股票看图软件_增强版.spec
echo     pathex=[], >> 股票看图软件_增强版.spec
echo     binaries=[], >> 股票看图软件_增强版.spec
echo     datas=added_files, >> 股票看图软件_增强版.spec
echo     hiddenimports=['tkinter', 'tushare', 'pandas', 'matplotlib', 'selenium'], >> 股票看图软件_增强版.spec
echo     hookspath=[], >> 股票看图软件_增强版.spec
echo     hooksconfig={}, >> 股票看图软件_增强版.spec
echo     runtime_hooks=[], >> 股票看图软件_增强版.spec
echo     excludes=[], >> 股票看图软件_增强版.spec
echo     win_no_prefer_redirects=False, >> 股票看图软件_增强版.spec
echo     win_private_assemblies=False, >> 股票看图软件_增强版.spec
echo     cipher=block_cipher, >> 股票看图软件_增强版.spec
echo     noarchive=False, >> 股票看图软件_增强版.spec
echo ^) >> 股票看图软件_增强版.spec
echo. >> 股票看图软件_增强版.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher^) >> 股票看图软件_增强版.spec
echo. >> 股票看图软件_增强版.spec
echo exe = EXE( >> 股票看图软件_增强版.spec
echo     pyz, >> 股票看图软件_增强版.spec
echo     a.scripts, >> 股票看图软件_增强版.spec
echo     a.binaries, >> 股票看图软件_增强版.spec
echo     a.zipfiles, >> 股票看图软件_增强版.spec
echo     a.datas, >> 股票看图软件_增强版.spec
echo     [], >> 股票看图软件_增强版.spec
echo     name='股票看图软件_增强版', >> 股票看图软件_增强版.spec
echo     debug=False, >> 股票看图软件_增强版.spec
echo     bootloader_ignore_signals=False, >> 股票看图软件_增强版.spec
echo     strip=False, >> 股票看图软件_增强版.spec
echo     upx=True, >> 股票看图软件_增强版.spec
echo     upx_exclude=[], >> 股票看图软件_增强版.spec
echo     runtime_tmpdir=None, >> 股票看图软件_增强版.spec
echo     console=False, >> 股票看图软件_增强版.spec
echo     disable_windowed_traceback=False, >> 股票看图软件_增强版.spec
echo     argv_emulation=False, >> 股票看图软件_增强版.spec
echo     target_arch=None, >> 股票看图软件_增强版.spec
echo     codesign_identity=None, >> 股票看图软件_增强版.spec
echo     entitlements_file=None, >> 股票看图软件_增强版.spec
echo ^) >> 股票看图软件_增强版.spec

echo ✓ 配置文件已创建

echo.
echo 正在开始打包，这可能需要几分钟...
pyinstaller --clean 股票看图软件_增强版.spec

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ✓ 打包完成！
echo.
echo 📁 exe文件位置: dist\股票看图软件_增强版.exe
echo 📁 完整程序目录: dist\
echo.
echo 使用说明:
echo 1. 进入dist目录
echo 2. 双击运行 股票看图软件_增强版.exe
echo 3. 首次运行可能需要较长时间加载
echo.
pause
