#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用者监控功能
"""

import pandas as pd
import numpy as np
from datetime import datetime
from 使用者监控 import UserMonitor, set_user_phone, monitor_backtest_completion

def create_test_backtest_results():
    """创建测试回测结果"""
    # 模拟资产曲线数据
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    initial_capital = 100000
    
    # 模拟价格变化
    np.random.seed(42)
    daily_returns = np.random.normal(0.001, 0.02, len(dates))  # 平均日收益0.1%，波动率2%
    cumulative_returns = np.cumprod(1 + daily_returns)
    total_values = initial_capital * cumulative_returns
    
    equity_curve = pd.DataFrame({
        'date': dates,
        'total_value': total_values,
        'cash': total_values * 0.1,  # 假设10%现金
        'positions_value': total_values * 0.9,  # 90%持仓
        'daily_return': daily_returns
    })
    
    # 模拟交易记录
    trades_data = []
    for i in range(10):  # 模拟10笔交易
        buy_date = dates[i * 30]
        sell_date = dates[i * 30 + 15] if i * 30 + 15 < len(dates) else dates[-1]
        buy_price = 10 + np.random.random() * 5
        sell_price = buy_price * (1 + np.random.normal(0.05, 0.1))  # 平均5%收益
        
        trades_data.extend([
            {
                'symbol': 'TEST',
                'action': 'BUY',
                'quantity': 1000,
                'price': buy_price,
                'date': buy_date.strftime('%Y-%m-%d'),
                'commission': buy_price * 1000 * 0.001,
                'value': buy_price * 1000
            },
            {
                'symbol': 'TEST',
                'action': 'SELL',
                'quantity': 1000,
                'price': sell_price,
                'date': sell_date.strftime('%Y-%m-%d'),
                'commission': sell_price * 1000 * 0.001,
                'value': sell_price * 1000
            }
        ])
    
    trades = pd.DataFrame(trades_data)
    
    # 计算回测指标
    final_value = total_values[-1]
    total_return = (final_value - initial_capital) / initial_capital
    trading_days = len(dates)
    annual_return = (final_value / initial_capital) ** (252 / trading_days) - 1
    volatility = equity_curve['daily_return'].std() * np.sqrt(252)
    
    # 计算最大回撤
    rolling_max = equity_curve['total_value'].expanding().max()
    drawdown = (equity_curve['total_value'] - rolling_max) / rolling_max
    max_drawdown = drawdown.min()
    
    # 计算夏普比率
    risk_free_rate = 0.03
    sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
    
    # 计算交易统计
    buy_trades = trades[trades['action'] == 'BUY']
    sell_trades = trades[trades['action'] == 'SELL']
    total_trades = len(buy_trades)
    
    # 计算胜率
    trade_returns = []
    for i in range(min(len(buy_trades), len(sell_trades))):
        buy_price = buy_trades.iloc[i]['price']
        sell_price = sell_trades.iloc[i]['price']
        trade_return = (sell_price - buy_price) / buy_price
        trade_returns.append(trade_return)
    
    win_trades = len([r for r in trade_returns if r > 0])
    win_rate = win_trades / len(trade_returns) if trade_returns else 0
    
    results = {
        'initial_capital': initial_capital,
        'final_value': final_value,
        'total_return': total_return,
        'annual_return': annual_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'total_trades': total_trades,
        'win_rate': win_rate,
        'equity_curve': equity_curve,
        'trades': trades
    }
    
    return results

def create_test_custom_strategy():
    """创建测试自定义策略代码"""
    return """
# 测试自定义策略
# 这是一个简单的移动平均策略

# 计算移动平均线
data['MA5'] = data['close'].rolling(window=5).mean()
data['MA20'] = data['close'].rolling(window=20).mean()

# 生成交易信号
data['signal'] = 0
data.loc[data['MA5'] > data['MA20'], 'signal'] = 1  # 买入信号
data.loc[data['MA5'] < data['MA20'], 'signal'] = -1  # 卖出信号

# 计算持仓
data['position'] = data['signal'].shift(1).fillna(0)
"""

def test_user_monitor():
    """测试使用者监控功能"""
    print("=" * 60)
    print("开始测试使用者监控功能")
    print("=" * 60)
    
    # 1. 测试设置手机号
    print("\n1. 测试设置手机号...")
    test_phone = "13812345678"
    set_user_phone(test_phone)
    print(f"设置手机号: {test_phone}")
    
    # 2. 创建监控器实例
    print("\n2. 创建监控器实例...")
    monitor = UserMonitor()
    phone_suffix = monitor.get_phone_suffix()
    print(f"获取到手机号后四位: {phone_suffix}")
    
    # 3. 测试文件名生成
    print("\n3. 测试文件名生成...")
    filename = monitor.generate_filename()
    print(f"生成的文件名: {filename}")
    
    # 4. 创建测试回测结果
    print("\n4. 创建测试回测结果...")
    backtest_results = create_test_backtest_results()
    custom_strategy_code = create_test_custom_strategy()
    print(f"回测结果创建完成，总收益率: {backtest_results['total_return']:.2%}")
    
    # 5. 测试数据收集
    print("\n5. 测试数据收集...")
    collected_data = monitor.collect_backtest_data(backtest_results, custom_strategy_code)
    print(f"数据收集完成，时间戳: {collected_data['timestamp']}")
    
    # 6. 测试XLSX文件生成
    print("\n6. 测试XLSX文件生成...")
    filepath = monitor.create_xlsx_file(collected_data)
    if filepath:
        print(f"XLSX文件生成成功: {filepath}")
        
        # 验证文件内容
        try:
            import os
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"文件大小: {file_size} 字节")
            else:
                print("文件不存在")
        except Exception as e:
            print(f"验证文件时出错: {e}")
    else:
        print("XLSX文件生成失败")
    
    # 7. 测试完整流程
    print("\n7. 测试完整监控流程...")
    success = monitor_backtest_completion(backtest_results, custom_strategy_code)
    if success:
        print("✅ 完整监控流程测试成功")
    else:
        print("❌ 完整监控流程测试失败")
    
    print("\n" + "=" * 60)
    print("使用者监控功能测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_user_monitor()
