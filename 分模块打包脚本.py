#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票看图软件分模块打包脚本
将程序打包成多个文件，便于分发和更新
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ PyInstaller安装失败")
        return False

def create_spec_file_onedir():
    """创建分目录打包的PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件
added_files = [
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('策略示例', '策略示例'),
    ('东方财富交易界面源码.txt', '.'),
    ('东方财富持仓网页.txt', '.'),
    ('东方财富登录源代码.txt', '.'),
    ('东方财富账户分析.txt', '.'),
]

a = Analysis(
    ['股票看图软件_增强版.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tushare',
        'pandas',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'numpy',
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.edge.options',
        'openpyxl',
        'xlrd',
        'requests',
        'lxml',
        'bs4',
        'datetime',
        'threading',
        'json',
        'time',
        'importlib',
        'importlib.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='股票看图软件_增强版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='股票看图软件_增强版',
)
'''
    
    with open('股票看图软件_增强版_分模块.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已创建分模块PyInstaller配置文件: 股票看图软件_增强版_分模块.spec")

def build_onedir():
    """构建分目录版本"""
    print("开始构建分模块版本...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "股票看图软件_增强版_分模块.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='gbk', errors='ignore')
        
        if result.returncode == 0:
            print("✓ 分模块版本构建成功！")
            print(f"输出目录: {os.path.abspath('dist/股票看图软件_增强版')}")
            return True
        else:
            print("❌ 分模块版本构建失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现异常: {str(e)}")
        return False

def copy_additional_files():
    """复制额外需要的文件到dist目录"""
    dist_dir = Path("dist/股票看图软件_增强版")
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 需要复制的文件列表
    files_to_copy = [
        "requirements.txt",
        "网页交易界面优化总结.md",
        "打包完成说明.md",
        "打包成功总结.md",
    ]
    
    print("正在复制额外文件...")
    
    # 复制文件
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"✓ 已复制: {file_name}")
            except Exception as e:
                print(f"⚠ 复制文件失败 {file_name}: {str(e)}")
    
    return True

def create_startup_script():
    """创建启动脚本"""
    dist_dir = Path("dist/股票看图软件_增强版")
    
    # 创建批处理启动脚本
    bat_content = '''@echo off
chcp 65001 >nul
echo 正在启动股票看图软件...
echo.
echo 首次启动可能需要较长时间，请耐心等待...
echo.
start "" "股票看图软件_增强版.exe"
'''
    
    bat_file = dist_dir / "启动程序.bat"
    with open(bat_file, 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print(f"✓ 已创建启动脚本: {bat_file}")

def create_readme():
    """创建README文件"""
    dist_dir = Path("dist/股票看图软件_增强版")
    
    readme_content = f"""# 股票看图软件 - 增强版（分模块版本）

## 软件介绍
这是股票看图软件的分模块版本，将程序和依赖库分开存放，便于更新和维护。

## 文件结构
```
股票看图软件_增强版/
├── 股票看图软件_增强版.exe    # 主程序
├── 启动程序.bat              # 启动脚本
├── _internal/               # 依赖库目录
│   ├── *.dll               # 动态链接库
│   ├── *.pyd               # Python扩展模块
│   └── ...                 # 其他依赖文件
├── 策略示例/                # 策略示例文件
├── 回测系统.py              # 回测引擎
├── 回测分析.py              # 回测分析
├── 策略模板.py              # 策略模板
├── 多股票回测系统.py        # 多股票回测
├── 技术指标库.py            # 技术指标
└── README.md               # 本文件
```

## 使用方法

### 方法1：双击启动脚本
```
双击 "启动程序.bat" 文件
```

### 方法2：直接运行主程序
```
双击 "股票看图软件_增强版.exe" 文件
```

## 优势特点

### 分模块优势
- **文件大小**: 主程序文件较小，便于传输
- **更新方便**: 可以单独更新程序或依赖库
- **启动速度**: 相比单文件版本启动更快
- **维护性**: 便于问题排查和维护

### 功能特点
- **股票分析**: K线图、技术指标、买卖点显示
- **策略回测**: MACD、KDJ、自定义策略回测
- **多股票回测**: 批量回测和策略对比
- **网页交易**: 自动化交易（支持滚动界面）
- **自定义策略**: Python策略编写和测试

## 系统要求
- Windows 10/11 (64位)
- 8GB以上内存
- 2GB可用硬盘空间
- 稳定的网络连接
- Microsoft Edge浏览器（用于网页交易）

## 注意事项
1. **完整复制**: 请确保复制整个文件夹，不要只复制exe文件
2. **路径问题**: 不要移动exe文件到其他位置，保持文件夹结构
3. **杀毒软件**: 可能被误报，请添加到白名单
4. **网络连接**: 需要网络连接获取股票数据
5. **谨慎交易**: 网页交易涉及真实资金，请谨慎使用

## 故障排除
1. **启动失败**: 检查是否完整复制了所有文件
2. **缺少文件**: 确保_internal目录完整
3. **网络问题**: 检查网络连接和防火墙设置
4. **权限问题**: 以管理员身份运行

## 版本信息
- 版本: 增强版 v1.0 (分模块版)
- 构建日期: {datetime.now().strftime("%Y-%m-%d")}
- Python版本: 3.11.8
- PyInstaller版本: 6.12.0

## 免责声明
本软件仅供学习和研究使用，投资有风险，入市需谨慎。
使用本软件进行实际交易的风险由用户自行承担。

祝您使用愉快！
"""
    
    readme_file = dist_dir / "README.md"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 已创建README文件: {readme_file}")

def get_folder_size(folder_path):
    """计算文件夹大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for filename in filenames:
            filepath = os.path.join(dirpath, filename)
            if os.path.exists(filepath):
                total_size += os.path.getsize(filepath)
    return total_size

def format_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def analyze_build_result():
    """分析构建结果"""
    dist_dir = Path("dist/股票看图软件_增强版")
    if not dist_dir.exists():
        print("❌ 构建目录不存在")
        return
    
    print("\n" + "="*60)
    print("📊 构建结果分析")
    print("="*60)
    
    # 主程序大小
    exe_file = dist_dir / "股票看图软件_增强版.exe"
    if exe_file.exists():
        exe_size = exe_file.stat().st_size
        print(f"📱 主程序大小: {format_size(exe_size)}")
    
    # _internal目录大小
    internal_dir = dist_dir / "_internal"
    if internal_dir.exists():
        internal_size = get_folder_size(internal_dir)
        print(f"📦 依赖库大小: {format_size(internal_size)}")
    
    # 总大小
    total_size = get_folder_size(dist_dir)
    print(f"📁 总大小: {format_size(total_size)}")
    
    # 文件数量统计
    file_count = 0
    dll_count = 0
    pyd_count = 0
    
    for root, dirs, files in os.walk(dist_dir):
        file_count += len(files)
        for file in files:
            if file.endswith('.dll'):
                dll_count += 1
            elif file.endswith('.pyd'):
                pyd_count += 1
    
    print(f"📄 文件总数: {file_count}")
    print(f"🔗 DLL文件: {dll_count}")
    print(f"🐍 PYD文件: {pyd_count}")

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("正在清理临时文件...")
    
    dirs_to_clean = ['build', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 已清理目录: {dir_name}")
            except Exception as e:
                print(f"⚠ 清理目录失败 {dir_name}: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("股票看图软件 - 分模块打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('股票看图软件_增强版.py'):
        print("❌ 未找到主程序文件: 股票看图软件_增强版.py")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建spec配置文件
    create_spec_file_onedir()
    
    # 构建分模块版本
    if not build_onedir():
        return False
    
    # 复制额外文件
    copy_additional_files()
    
    # 创建启动脚本
    create_startup_script()
    
    # 创建README文件
    create_readme()
    
    # 分析构建结果
    analyze_build_result()
    
    # 清理临时文件
    clean_build_files()
    
    print("\n" + "=" * 60)
    print("🎉 分模块打包完成！")
    print("=" * 60)
    print(f"📁 程序目录: {os.path.abspath('dist/股票看图软件_增强版')}")
    print(f"🚀 启动方式: 双击 '启动程序.bat' 或 '股票看图软件_增强版.exe'")
    print("\n📋 分模块版本优势:")
    print("✓ 主程序文件较小，便于传输")
    print("✓ 可以单独更新程序或依赖库")
    print("✓ 启动速度比单文件版本更快")
    print("✓ 便于问题排查和维护")
    print("\n⚠ 重要提示:")
    print("• 请复制整个文件夹，不要只复制exe文件")
    print("• 保持文件夹结构，不要移动exe文件位置")
    print("• 首次运行可能被杀毒软件误报，请添加白名单")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n✅ 打包成功！按回车键退出...")
        else:
            input("\n❌ 打包失败！按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生未预期的错误: {str(e)}")
        input("按回车键退出...")
