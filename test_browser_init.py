#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器初始化改进
"""

import sys
import os
import time
import threading

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_browser_initialization():
    """测试浏览器初始化功能"""
    print("开始测试浏览器初始化...")
    
    try:
        # 导入TushareUI类
        from 登录注册 import TushareUI
        
        print("正在创建TushareUI实例...")
        
        # 创建实例（这会触发浏览器初始化）
        ui = TushareUI()
        
        print("TushareUI实例创建完成")
        
        # 测试浏览器连接检查
        if hasattr(ui, 'is_driver_alive'):
            print(f"浏览器连接状态: {ui.is_driver_alive()}")
        
        # 测试确保驱动准备就绪
        if hasattr(ui, 'ensure_driver_ready'):
            print(f"确保驱动准备就绪: {ui.ensure_driver_ready()}")
        
        # 检查浏览器类型
        if hasattr(ui, 'browser_type') and ui.browser_type:
            print(f"使用的浏览器类型: {ui.browser_type}")
        
        # 检查驱动初始化状态
        if hasattr(ui, 'driver_initialized'):
            print(f"驱动初始化状态: {ui.driver_initialized}")
        
        # 测试获取验证码功能（不启动UI）
        print("测试验证码获取功能...")
        if hasattr(ui, 'get_captcha'):
            # 在后台线程中测试
            def test_captcha():
                try:
                    ui.get_captcha()
                    print("验证码获取测试完成")
                except Exception as e:
                    print(f"验证码获取测试失败: {str(e)}")
            
            captcha_thread = threading.Thread(target=test_captcha, daemon=True)
            captcha_thread.start()
            captcha_thread.join(timeout=10)  # 等待最多10秒
            
            if captcha_thread.is_alive():
                print("验证码获取测试超时")
        
        # 清理资源
        print("正在清理资源...")
        if hasattr(ui, 'driver') and ui.driver:
            try:
                ui.driver.quit()
                print("浏览器驱动已关闭")
            except Exception as e:
                print(f"关闭浏览器驱动时出错: {str(e)}")
        
        print("测试完成")
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_browser_retry_mechanism():
    """测试浏览器重试机制"""
    print("\n开始测试浏览器重试机制...")
    
    try:
        from 登录注册 import TushareUI
        
        ui = TushareUI()
        
        # 测试重试机制
        if hasattr(ui, 'retry_browser_operation'):
            def test_operation():
                # 模拟一个简单的浏览器操作
                if ui.driver:
                    return ui.driver.current_url
                else:
                    raise Exception("浏览器驱动不可用")
            
            try:
                result = ui.retry_browser_operation(test_operation)
                print(f"重试机制测试成功，结果: {result}")
            except Exception as e:
                print(f"重试机制测试失败: {str(e)}")
        
        # 清理
        if hasattr(ui, 'driver') and ui.driver:
            try:
                ui.driver.quit()
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"重试机制测试出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("浏览器初始化改进测试")
    print("=" * 50)
    
    # 测试基本初始化
    success1 = test_browser_initialization()
    
    # 测试重试机制
    success2 = test_browser_retry_mechanism()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"基本初始化测试: {'通过' if success1 else '失败'}")
    print(f"重试机制测试: {'通过' if success2 else '失败'}")
    print("=" * 50)
    
    if success1 and success2:
        print("所有测试通过！")
        sys.exit(0)
    else:
        print("部分测试失败！")
        sys.exit(1)
