

web = Edge()
web.get('https://jywg.18.cn/Trade/Buy')
#浏览器巨大化
# web.maximize_window() 
web.find_element(By.ID, "tKtZjzh").send_keys(im.dczh)
web.find_element(By.ID, "txtPwd").send_keys(im.dcmm)
res = web.find_element(By.ID,"imgValidCode")
png_path = 'login.png'
#保存文件路径
with open(png_path，'wb'） as file:
    file.write(res.screenshot_as_png)
#识别验证码

res = cjy.PostPic(im,1902)
#1902验证码类型
time.sleep(1)
web.find_element(By.ID,'txtValidCode'）.send_keys(data)I#填入验证码 time.sleep(1)
web.find_element(By.ID,'rdsc45').click()
#点击在线时间
web.find_element(By.ID,'btnConfirm').click() time.sleep(1)
#点击登录
Buy="//*[@id='main']/div/div[2]/div[1]/ul/1i[2]/a"
Sell ="//*[@id='main']/div/div[2]/div[1]/ul/li[3]/a"
def main_func(BS):
    web.find_element(By.XPATH,BS).click() #买入卖出按钮

    time.sleep(1)
    web.find_element(By.ID,'btnReset').click()#点击清空数据
    time.sleep(0.5)
    web.find_element(By.ID,'txtCode').send_keys(im.code) #输入股票代码
    time.sleep(0.5)
    
    web.find_element(By.ID,'iptPrice').click()#点击价格
    time.sleep(0.5)
    web.find_element(By.ID,'iptPrice').clear()#清空默认输入的现价
    time.sleep(0.5)
    web.find_element(By.ID,'iptPrice').send_keys(frame[i,3])#输入价格
    time.sleep(0.5)
    web.find_element(By.ID,'iptAmount').click()#点击数量
    time.sleep(0.5)
    web.find_element(By.ID,'iptAmount').clear()#清空默认输入的数量
    time.sleep(0.5)
    web.find_element(By.ID,'iptAmount').send_keys(frame[i,4])#输入数量
    time.sleep(0.5)
    web.find_element(By.ID,'btnConfirm').click()#点击确认
    time.sleep(0.5)
    web.find_element(By.XPATH,'/html/body/table/tbody/tr[2]/td[2]/div/div[1]/div[2]/div[1]/div/div[2]/button').click()#点击确定
    time.sleep(0.5)
    web.find_element(By.ID,'btnCxcConfirm').click()#提醒返回
    time.sleep(0.5)
    return

#判断交易
for i in range(length):
    if frame[i，θ] =='B':
             main_func(Buy)
    elif frame[i,θ] =='S':
        main_func(Sell)
    else:
        print()
    time.sleep(10)    