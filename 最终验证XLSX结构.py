#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证XLSX文件结构和内容
"""

import pandas as pd
import os

def verify_final_xlsx_structure():
    """验证最终的XLSX文件结构"""
    print("=" * 80)
    print("最终验证XLSX文件结构和内容")
    print("=" * 80)
    
    # 查找最新的XLSX文件
    config_dir = "user_config"
    xlsx_files = [f for f in os.listdir(config_dir) if f.endswith('.xlsx')]
    
    if not xlsx_files:
        print("❌ 未找到XLSX文件")
        return
    
    # 使用最新的文件
    latest_file = max(xlsx_files, key=lambda x: os.path.getctime(os.path.join(config_dir, x)))
    filepath = os.path.join(config_dir, latest_file)
    file_size = os.path.getsize(filepath)
    
    print(f"📁 验证文件: {latest_file}")
    print(f"📊 文件大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
    print()
    
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(filepath)
        sheet_names = excel_file.sheet_names
        print(f"📋 工作表列表: {sheet_names}")
        print()
        
        # 1. 验证回测结果表
        print("🔍 1. 回测结果表")
        print("-" * 50)
        if '回测结果' in sheet_names:
            df_results = pd.read_excel(filepath, sheet_name='回测结果')
            print(f"列数: {len(df_results.columns)}")
            print(f"行数: {len(df_results)}")
            print(f"列名: {list(df_results.columns)}")
            
            # 检查关键列
            key_columns = ['时间戳', '手机号', '自定义策略代码']
            for col in key_columns:
                if col in df_results.columns:
                    value = df_results.iloc[0][col]
                    if col == '自定义策略代码':
                        print(f"✅ {col}: 存在 (长度: {len(str(value))} 字符)")
                        print(f"   策略代码预览: {str(value)[:100]}...")
                    elif col == '手机号':
                        print(f"✅ {col}: {value} (长度: {len(str(value))})")
                    else:
                        print(f"✅ {col}: {value}")
                else:
                    print(f"❌ {col}: 缺失")
            print()
        
        # 2. 验证自定义公式表
        print("🔍 2. 自定义公式表")
        print("-" * 50)
        if '自定义公式' in sheet_names:
            df_strategy = pd.read_excel(filepath, sheet_name='自定义公式')
            print(f"列数: {len(df_strategy.columns)}")
            print(f"行数: {len(df_strategy)}")
            print(f"列名: {list(df_strategy.columns)}")
            
            strategy_code = df_strategy.iloc[0]['自定义策略代码']
            phone_number = df_strategy.iloc[0]['手机号']
            timestamp = df_strategy.iloc[0]['时间戳']
            
            print(f"✅ 手机号: {phone_number}")
            print(f"✅ 时间戳: {timestamp}")
            print(f"✅ 策略代码长度: {len(strategy_code)} 字符")
            print(f"   策略代码预览: {strategy_code[:150]}...")
            print()
        
        # 3. 验证资产曲线表
        print("🔍 3. 资产曲线表")
        print("-" * 50)
        if '资产曲线' in sheet_names:
            df_equity = pd.read_excel(filepath, sheet_name='资产曲线')
            print(f"列数: {len(df_equity.columns)}")
            print(f"行数: {len(df_equity)}")
            print(f"列名: {list(df_equity.columns)}")
            if len(df_equity) > 0:
                print("前3行数据:")
                print(df_equity.head(3).to_string())
            print()
        
        # 4. 验证交易记录表
        print("🔍 4. 交易记录表")
        print("-" * 50)
        if '交易记录' in sheet_names:
            df_trades = pd.read_excel(filepath, sheet_name='交易记录')
            print(f"列数: {len(df_trades.columns)}")
            print(f"行数: {len(df_trades)}")
            print(f"列名: {list(df_trades.columns)}")
            if len(df_trades) > 0:
                print("前3行数据:")
                print(df_trades.head(3).to_string())
            print()
        
        # 5. 数据一致性验证
        print("🔍 5. 数据一致性验证")
        print("-" * 50)
        
        # 验证手机号一致性
        if '回测结果' in sheet_names and '自定义公式' in sheet_names:
            results_phone = df_results.iloc[0]['手机号']
            strategy_phone = df_strategy.iloc[0]['手机号']
            
            if results_phone == strategy_phone:
                print(f"✅ 手机号一致: {results_phone}")
            else:
                print(f"❌ 手机号不一致: 回测结果表({results_phone}) vs 自定义公式表({strategy_phone})")
        
        # 验证策略代码一致性
        if '回测结果' in sheet_names and '自定义公式' in sheet_names:
            if '自定义策略代码' in df_results.columns:
                results_strategy = df_results.iloc[0]['自定义策略代码']
                strategy_strategy = df_strategy.iloc[0]['自定义策略代码']
                
                if results_strategy == strategy_strategy:
                    print(f"✅ 策略代码一致 (长度: {len(results_strategy)} 字符)")
                else:
                    print(f"❌ 策略代码不一致")
                    print(f"   回测结果表长度: {len(results_strategy)}")
                    print(f"   自定义公式表长度: {len(strategy_strategy)}")
        
        # 6. 总结
        print("\n🎯 验证总结")
        print("-" * 50)
        
        required_elements = [
            ('回测结果表', '回测结果' in sheet_names),
            ('自定义公式表', '自定义公式' in sheet_names),
            ('资产曲线表', '资产曲线' in sheet_names),
            ('交易记录表', '交易记录' in sheet_names),
            ('完整手机号', '回测结果' in sheet_names and df_results.iloc[0]['手机号']),
            ('自定义策略代码', '回测结果' in sheet_names and '自定义策略代码' in df_results.columns),
        ]
        
        all_passed = True
        for element, status in required_elements:
            if status:
                print(f"✅ {element}: 正常")
            else:
                print(f"❌ {element}: 缺失")
                all_passed = False
        
        print()
        if all_passed:
            print("🎉 所有验证项目通过！XLSX文件结构完整，包含所有必要数据。")
        else:
            print("⚠️ 部分验证项目未通过，请检查文件生成逻辑。")
        
        # 7. 显示完整的回测结果表结构
        print("\n📊 完整的回测结果表结构")
        print("-" * 50)
        if '回测结果' in sheet_names:
            print("列名及数据类型:")
            for i, col in enumerate(df_results.columns, 1):
                value = df_results.iloc[0][col]
                data_type = type(value).__name__
                if col == '自定义策略代码':
                    print(f"{i:2d}. {col:<15} | {data_type:<10} | (长度: {len(str(value))} 字符)")
                elif isinstance(value, float):
                    print(f"{i:2d}. {col:<15} | {data_type:<10} | {value:.4f}")
                else:
                    print(f"{i:2d}. {col:<15} | {data_type:<10} | {str(value)[:30]}")
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_final_xlsx_structure()
