#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多股票监控系统测试脚本
用于验证多股票监控功能是否正常工作
"""

import sys
import time
import threading
from datetime import datetime
import pandas as pd
import numpy as np

# 模拟市场数据管理器
class MockMarketDataManager:
    """模拟市场数据管理器，用于测试"""
    
    def __init__(self):
        self.stock_codes = [
            "000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "000858.SZ"
        ]
        self.base_prices = {
            "000001.SZ": 10.50,
            "000002.SZ": 25.80,
            "600000.SH": 8.90,
            "600036.SH": 35.60,
            "000858.SZ": 12.30
        }
    
    def get_realtime_data(self, ts_code, use_cache=True):
        """模拟获取实时数据"""
        if ts_code not in self.base_prices:
            return pd.DataFrame()
        
        # 模拟价格波动
        base_price = self.base_prices[ts_code]
        change_pct = np.random.uniform(-0.05, 0.05)  # ±5%波动
        current_price = base_price * (1 + change_pct)
        
        # 生成模拟数据
        data = {
            'ts_code': ts_code,
            'trade_date': datetime.now().strftime('%Y%m%d'),
            'open': current_price * 0.99,
            'high': current_price * 1.02,
            'low': current_price * 0.98,
            'close': current_price,
            'vol': np.random.randint(1000000, 10000000),
            'amount': current_price * np.random.randint(1000000, 10000000)
        }
        
        return pd.DataFrame([data])
    
    def get_sector_stocks_enhanced(self, sector_name):
        """模拟获取板块股票"""
        if sector_name == "沪深300":
            return self.stock_codes[:3]
        elif sector_name == "中证1000":
            return self.stock_codes[2:]
        else:
            return self.stock_codes


def test_multi_stock_monitor():
    """测试多股票监控功能"""
    print("=" * 60)
    print("多股票监控系统测试")
    print("=" * 60)
    
    try:
        # 导入多股票监控模块
        from 多股票监控管理器 import MultiStockMonitor
        from 交易调度器 import TradingScheduler
        from 多股票监控配置 import get_config
        
        print("✓ 模块导入成功")
        
        # 创建模拟数据管理器
        mock_data_manager = MockMarketDataManager()
        print("✓ 模拟数据管理器创建成功")
        
        # 创建多股票监控器
        monitor = MultiStockMonitor(mock_data_manager, max_stocks=10)
        print("✓ 多股票监控器创建成功")
        
        # 设置回调函数
        def on_data_update(all_status):
            print(f"📊 数据更新回调: {len(all_status)} 只股票")
            for ts_code, status in all_status.items():
                price = status.get('current_price', 0)
                signal = status.get('last_signal', 0)
                signal_text = "买入" if signal == 1 else "卖出" if signal == -1 else "无"
                print(f"  {ts_code}: {price:.2f} 信号:{signal_text}")
        
        def on_signal_generated(signals):
            print(f"🔔 信号生成回调: {len(signals)} 个信号")
            for signal in signals:
                ts_code = signal['ts_code']
                signal_type = "买入" if signal['signal'] == 1 else "卖出"
                price = signal['price']
                print(f"  {ts_code} {signal_type}信号 @ {price:.2f}")
        
        def on_error(error_msg):
            print(f"❌ 错误回调: {error_msg}")
        
        monitor.set_callbacks(
            data_update_callback=on_data_update,
            signal_callback=on_signal_generated,
            error_callback=on_error
        )
        print("✓ 回调函数设置成功")
        
        # 添加测试股票
        test_stocks = ["000001.SZ", "000002.SZ", "600000.SH"]
        for stock_code in test_stocks:
            success = monitor.add_stock(stock_code, "MACD")
            if success:
                print(f"✓ 添加股票 {stock_code} 成功")
            else:
                print(f"❌ 添加股票 {stock_code} 失败")
        
        # 获取监控统计
        stats = monitor.get_statistics()
        print(f"✓ 监控统计: {stats}")
        
        # 启动监控
        print("\n开始监控测试...")
        monitor.start_monitoring(update_interval=5)  # 5秒更新间隔用于测试
        
        # 运行30秒
        print("监控运行中，30秒后停止...")
        time.sleep(30)
        
        # 停止监控
        monitor.stop_monitoring()
        print("✓ 监控已停止")
        
        # 获取最终统计
        final_stats = monitor.get_performance_metrics()
        print(f"✓ 最终性能指标: {final_stats}")
        
        print("\n" + "=" * 60)
        print("多股票监控测试完成！")
        print("=" * 60)
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保所有必要的文件都在当前目录中")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_trading_scheduler():
    """测试交易调度器功能"""
    print("\n" + "=" * 60)
    print("交易调度器测试")
    print("=" * 60)
    
    try:
        from 交易调度器 import TradingScheduler
        
        # 模拟交易执行函数
        def mock_trade_executor(action, ts_code, quantity, price):
            print(f"🔄 模拟执行交易: {action} {ts_code} {quantity}股 @ {price:.2f}")
            time.sleep(1)  # 模拟交易耗时
            return True  # 模拟交易成功
        
        # 创建交易调度器
        scheduler = TradingScheduler(trade_executor=mock_trade_executor)
        print("✓ 交易调度器创建成功")
        
        # 设置回调函数
        def on_signal_added(signal_dict):
            print(f"📋 信号已添加: {signal_dict['ts_code']} {signal_dict['action']}")
        
        def on_trade_completed(signal_dict):
            print(f"✅ 交易完成: {signal_dict['ts_code']} {signal_dict['action']}")
        
        def on_trading_error(error_msg):
            print(f"❌ 交易错误: {error_msg}")
        
        scheduler.set_callbacks(
            signal_callback=on_signal_added,
            trade_callback=on_trade_completed,
            error_callback=on_trading_error
        )
        print("✓ 回调函数设置成功")
        
        # 启动调度器
        scheduler.start_scheduler()
        print("✓ 交易调度器已启动")
        
        # 添加测试交易信号
        test_signals = [
            ("000001.SZ", "BUY", 10.50, 1000),
            ("000002.SZ", "BUY", 25.80, 500),
            ("600000.SH", "SELL", 8.90, 800),
        ]
        
        for ts_code, action, price, quantity in test_signals:
            success = scheduler.add_signal(ts_code, action, price, quantity)
            if success:
                print(f"✓ 添加交易信号: {ts_code} {action}")
            else:
                print(f"❌ 添加交易信号失败: {ts_code} {action}")
            time.sleep(1)
        
        # 等待交易完成
        print("等待交易完成...")
        time.sleep(10)
        
        # 获取队列状态
        queue_status = scheduler.get_queue_status()
        print(f"✓ 队列状态: {queue_status}")
        
        # 获取交易记录
        recent_trades = scheduler.get_recent_trades(limit=10)
        print(f"✓ 最近交易记录: {len(recent_trades)} 条")
        
        # 停止调度器
        scheduler.stop_scheduler()
        print("✓ 交易调度器已停止")
        
        print("\n" + "=" * 60)
        print("交易调度器测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 交易调度器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_config_manager():
    """测试配置管理器功能"""
    print("\n" + "=" * 60)
    print("配置管理器测试")
    print("=" * 60)
    
    try:
        from 多股票监控配置 import MultiStockConfig
        
        # 创建配置管理器
        config = MultiStockConfig("test_config.json")
        print("✓ 配置管理器创建成功")
        
        # 验证配置
        if config.validate_config():
            print("✓ 配置验证通过")
        else:
            print("❌ 配置验证失败")
        
        # 测试获取配置
        max_stocks = config.get('monitor', 'max_stocks')
        print(f"✓ 最大监控股票数: {max_stocks}")
        
        update_interval = config.get('monitor', 'default_update_interval')
        print(f"✓ 默认更新间隔: {update_interval}")
        
        # 测试修改配置
        config.set('monitor', 'max_stocks', 150)
        new_max_stocks = config.get('monitor', 'max_stocks')
        print(f"✓ 修改后最大监控股票数: {new_max_stocks}")
        
        # 测试保存配置
        config.save_config()
        print("✓ 配置保存成功")
        
        # 测试各种配置获取方法
        monitor_config = config.get_monitor_config()
        print(f"✓ 监控配置: {len(monitor_config)} 项")
        
        risk_config = config.get_risk_config()
        print(f"✓ 风险控制配置: {len(risk_config)} 项")
        
        print("\n" + "=" * 60)
        print("配置管理器测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("多股票监控系统完整测试")
    print("测试开始时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 运行所有测试
    test_config_manager()
    test_multi_stock_monitor()
    test_trading_scheduler()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("测试结束时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
