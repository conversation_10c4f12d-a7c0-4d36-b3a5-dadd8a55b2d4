# 浏览器连接改进说明

## 问题分析

根据您提供的日志，原始代码存在以下问题：

1. **Edge webdriver-manager失败**: URL `https://msedgedriver.azureedge.net/LATEST_RELEASE_129_WINDOWS` 无法访问
2. **Chrome webdriver-manager失败**: 网络连接问题 "Could not reach host. Are you offline?"
3. **浏览器连接丢失**: 运行过程中浏览器连接会丢失，需要重新初始化

## 改进措施

### 1. 优化浏览器初始化顺序

**改进前**: Edge优先 → Chrome备用
**改进后**: Chrome系统驱动优先 → Edge系统驱动 → webdriver-manager备用

```python
def init_driver(self):
    """智能初始化浏览器驱动（自动选择Edge或Chrome）"""
    # 清理之前的驱动实例
    if hasattr(self, 'driver') and self.driver:
        try:
            self.driver.quit()
        except:
            pass
        self.driver = None
    
    # 首先尝试Chrome浏览器（系统驱动优先）
    if self.try_init_chrome():
        return True

    # Chrome失败，尝试Edge浏览器
    if self.try_init_edge():
        return True

    # 两个都失败
    self.log_status("所有浏览器初始化都失败")
    return False
```

### 2. 优先使用系统驱动

**Chrome初始化改进**:
- 优先尝试系统Chrome驱动（更稳定，无需网络下载）
- 系统驱动失败后才尝试webdriver-manager
- 添加连接测试确保驱动可用

**Edge初始化改进**:
- 同样优先使用系统驱动
- 添加更多稳定性选项
- 增强错误处理

### 3. 添加连接健康检查

```python
def is_driver_alive(self):
    """检查浏览器驱动是否仍然活跃"""
    try:
        if not self.driver:
            return False
        # 尝试获取当前URL来测试连接
        self.driver.current_url
        return True
    except Exception as e:
        self.log_status(f"浏览器连接检查失败: {str(e)}")
        return False
```

### 4. 智能重新初始化机制

```python
def ensure_driver_ready(self):
    """确保浏览器驱动准备就绪"""
    if not self.driver_initialized or not self.is_driver_alive():
        self.log_status("浏览器连接丢失，正在重新初始化...")
        if not self.init_driver():
            self.log_status("浏览器重新初始化失败")
            return False
        self.driver_initialized = True
        self.log_status("浏览器重新初始化完成")
    return True
```

### 5. 重试机制

```python
def retry_browser_operation(self, operation, max_retries=3):
    """重试浏览器操作，处理连接丢失"""
    for attempt in range(max_retries):
        try:
            if not self.ensure_driver_ready():
                continue
            return operation()
        except Exception as e:
            self.log_status(f"浏览器操作失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                self.driver_initialized = False  # 标记需要重新初始化
                time.sleep(1)  # 短暂等待后重试
            else:
                raise e
    return None
```

### 6. 改进超时机制

- 将浏览器初始化超时从30秒减少到20秒
- 添加强制终止卡住进程的机制
- 改进错误信息收集

### 7. 线程安全的日志系统

```python
def log_status(self, message):
    """在状态框中显示消息（现在隐藏，只在控制台输出）"""
    # 在控制台输出日志（用于调试）
    print(f"{time.strftime('%H:%M:%S')} - {message}")
    # 安全地更新UI（只在主线程且UI运行时）
    try:
        if hasattr(self, 'root') and self.root and threading.current_thread() is threading.main_thread():
            self.root.update()
    except Exception:
        # 忽略UI更新错误，不影响核心功能
        pass
```

## 测试结果

运行 `test_browser_init.py` 的测试结果显示：

```
浏览器初始化改进测试
==================================================
开始测试浏览器初始化...
正在创建TushareUI实例...
19:19:14 - 正在尝试初始化Chrome浏览器...
19:19:15 - Chrome浏览器初始化成功（使用系统驱动）
浏览器连接状态: True
确保驱动准备就绪: True
使用的浏览器类型: chrome
驱动初始化状态: True
重试机制测试成功，结果: about:blank

==================================================
测试结果:
基本初始化测试: 通过
重试机制测试: 通过
==================================================
所有测试通过！
```

## 主要改进效果

1. **更快的初始化**: 优先使用系统驱动，避免网络下载延迟
2. **更高的成功率**: 多重备用方案，提高浏览器初始化成功率
3. **自动恢复**: 连接丢失时自动重新初始化
4. **更好的错误处理**: 详细的错误信息和重试机制
5. **线程安全**: 避免UI线程冲突问题

## 使用建议

1. 确保系统已安装Chrome或Edge浏览器
2. 如果网络环境较差，系统驱动优先的策略会更稳定
3. 如果仍有连接问题，可以检查防火墙或代理设置
4. 建议定期更新浏览器版本以保持兼容性

这些改进应该能够显著提高浏览器连接的稳定性和可靠性。
