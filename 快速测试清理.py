#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试进程清理功能
"""

import os
import sys
import time
import subprocess
import psutil

def check_python_processes():
    """检查Python进程"""
    count = 0
    related_processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and len(cmdline) > 1:
                        script_name = os.path.basename(cmdline[1])
                        if any(keyword in script_name.lower() for keyword in ['登录', '股票', 'tushare']):
                            related_processes.append({
                                'pid': proc.info['pid'],
                                'script': script_name
                            })
                            count += 1
            except:
                pass
    except:
        pass
    
    return count, related_processes

def main():
    print("=" * 50)
    print("进程清理功能测试")
    print("=" * 50)
    
    # 检查当前进程
    count, processes = check_python_processes()
    print(f"当前相关Python进程数量: {count}")
    
    if processes:
        print("发现以下相关进程:")
        for proc in processes:
            print(f"  PID: {proc['pid']} - {proc['script']}")
    else:
        print("✅ 没有发现相关的后台进程")
    
    print("\n测试说明:")
    print("1. 运行 登录注册.py")
    print("2. 完成登录，启动股票软件")
    print("3. 关闭股票软件窗口")
    print("4. 再次运行此脚本检查是否有残留进程")
    
    print("\n如果发现残留进程，可以运行以下命令清理:")
    print("python 快速测试清理.py --clean")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--clean":
        print("正在清理相关进程...")
        count, processes = check_python_processes()
        
        if processes:
            for proc in processes:
                try:
                    pid = proc['pid']
                    if pid != os.getpid():
                        print(f"终止进程 {pid} - {proc['script']}")
                        if os.name == 'nt':
                            subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                         capture_output=True, check=False)
                        else:
                            os.kill(pid, 9)
                except Exception as e:
                    print(f"终止进程失败: {e}")
            
            # 清理临时文件
            temp_files = ["login_process.pid", "tushare_token.txt"]
            for temp_file in temp_files:
                temp_path = os.path.join(os.path.dirname(__file__), temp_file)
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                        print(f"删除临时文件: {temp_file}")
                    except:
                        pass
            
            print("清理完成")
        else:
            print("没有需要清理的进程")
    else:
        main()
