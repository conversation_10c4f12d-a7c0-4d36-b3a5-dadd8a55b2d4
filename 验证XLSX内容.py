#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的XLSX文件内容
"""

import pandas as pd
import os

def verify_xlsx_content():
    """验证XLSX文件内容"""
    # 查找最新的XLSX文件
    config_dir = "user_config"
    xlsx_files = [f for f in os.listdir(config_dir) if f.endswith('.xlsx')]
    
    if not xlsx_files:
        print("未找到XLSX文件")
        return
    
    # 使用最新的文件
    latest_file = max(xlsx_files, key=lambda x: os.path.getctime(os.path.join(config_dir, x)))
    filepath = os.path.join(config_dir, latest_file)
    
    print(f"验证文件: {latest_file}")
    print("=" * 60)
    
    try:
        # 读取所有工作表
        excel_file = pd.ExcelFile(filepath)
        sheet_names = excel_file.sheet_names
        print(f"工作表列表: {sheet_names}")
        print()
        
        # 验证回测结果表
        if '回测结果' in sheet_names:
            df_results = pd.read_excel(filepath, sheet_name='回测结果')
            print("【回测结果表】")
            print(f"列名: {list(df_results.columns)}")
            print("数据内容:")
            for col in df_results.columns:
                value = df_results.iloc[0][col]
                if col == '手机号':
                    print(f"  {col}: {value} (长度: {len(str(value))})")
                elif isinstance(value, float):
                    print(f"  {col}: {value:.4f}")
                else:
                    print(f"  {col}: {value}")
            print()
        
        # 验证自定义公式表
        if '自定义公式' in sheet_names:
            df_strategy = pd.read_excel(filepath, sheet_name='自定义公式')
            print("【自定义公式表】")
            print(f"列名: {list(df_strategy.columns)}")
            print("数据内容:")
            for col in df_strategy.columns:
                value = df_strategy.iloc[0][col]
                if col == '自定义策略代码':
                    print(f"  {col}: (长度: {len(str(value))} 字符)")
                    print(f"    前200字符: {str(value)[:200]}...")
                elif col == '手机号':
                    print(f"  {col}: {value} (长度: {len(str(value))})")
                else:
                    print(f"  {col}: {value}")
            print()
        
        # 验证资产曲线表
        if '资产曲线' in sheet_names:
            df_equity = pd.read_excel(filepath, sheet_name='资产曲线')
            print("【资产曲线表】")
            print(f"列名: {list(df_equity.columns)}")
            print(f"数据行数: {len(df_equity)}")
            print("前5行数据:")
            print(df_equity.head())
            print()
        
        # 验证交易记录表
        if '交易记录' in sheet_names:
            df_trades = pd.read_excel(filepath, sheet_name='交易记录')
            print("【交易记录表】")
            print(f"列名: {list(df_trades.columns)}")
            print(f"数据行数: {len(df_trades)}")
            print("前5行数据:")
            print(df_trades.head())
            print()
        
        # 文件大小信息
        file_size = os.path.getsize(filepath)
        print(f"文件大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
        
    except Exception as e:
        print(f"验证文件时出错: {e}")

if __name__ == "__main__":
    verify_xlsx_content()
