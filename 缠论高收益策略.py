"""
基于缠论的高收益率交易策略
分析波段结构、中枢、支撑阻力位，实现精准买卖点判断
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class ChanlunHighYieldStrategy:
    """缠论高收益策略"""
    
    def __init__(self):
        self.name = "缠论高收益策略"
        self.parameters = {
            # 缠论基础参数
            'fenxing_strength': 2.0,      # 分型强度阈值
            'bi_min_bars': 5,              # 笔的最小K线数
            'xianduan_min_bi': 3,          # 线段最小笔数
            'zhongshu_min_overlap': 0.3,   # 中枢最小重叠比例
            
            # 波段分析参数
            'trend_strength_period': 20,   # 趋势强度计算周期
            'support_resistance_period': 30, # 支撑阻力位计算周期
            'volume_confirm_ratio': 1.5,   # 成交量确认倍数
            
            # 交易参数
            'position_size': 0.8,          # 仓位大小
            'stop_loss_ratio': 0.03,       # 止损比例3%
            'take_profit_ratio': 0.08,     # 止盈比例8%
            'trailing_stop_ratio': 0.02,   # 移动止损2%
            
            # 优化参数
            'min_profit_ratio': 0.02,      # 最小盈利比例
            'max_hold_days': 30,           # 最大持仓天数
            'min_trade_interval': 3,       # 最小交易间隔
        }
        
        # 状态变量
        self.current_position = 0
        self.entry_price = 0
        self.entry_date = 0
        self.highest_price = 0
        self.trade_history = []
        
    def analyze_chanlun_structure(self, data: pd.DataFrame) -> Dict:
        """分析缠论结构"""
        # 1. 识别分型
        fenxing_points = self.identify_fenxing(data)
        
        # 2. 构造笔
        bi_lines = self.construct_bi(data, fenxing_points)
        
        # 3. 构造线段
        xianduan_lines = self.construct_xianduan(bi_lines)
        
        # 4. 识别中枢
        zhongshu_zones = self.identify_zhongshu(xianduan_lines)
        
        # 5. 分析波段结构
        wave_structure = self.analyze_wave_structure(xianduan_lines, zhongshu_zones)
        
        # 6. 计算支撑阻力位
        support_resistance = self.calculate_support_resistance(data, fenxing_points)
        
        return {
            'fenxing': fenxing_points,
            'bi': bi_lines,
            'xianduan': xianduan_lines,
            'zhongshu': zhongshu_zones,
            'wave_structure': wave_structure,
            'support_resistance': support_resistance
        }
    
    def identify_fenxing(self, data: pd.DataFrame) -> List[Dict]:
        """识别分型点"""
        fenxing = []
        
        for i in range(2, len(data) - 2):
            # 顶分型：当前高点高于前后两个高点
            if (data['high'].iloc[i] > data['high'].iloc[i-1] and 
                data['high'].iloc[i] > data['high'].iloc[i+1] and
                data['high'].iloc[i] > data['high'].iloc[i-2] and
                data['high'].iloc[i] > data['high'].iloc[i+2]):
                
                strength = self.calculate_fenxing_strength(data, i, 'top')
                if strength >= self.parameters['fenxing_strength']:
                    fenxing.append({
                        'index': i,
                        'type': 'top',
                        'price': data['high'].iloc[i],
                        'strength': strength,
                        'date': data.index[i] if hasattr(data.index[i], 'date') else i
                    })
            
            # 底分型：当前低点低于前后两个低点
            elif (data['low'].iloc[i] < data['low'].iloc[i-1] and 
                  data['low'].iloc[i] < data['low'].iloc[i+1] and
                  data['low'].iloc[i] < data['low'].iloc[i-2] and
                  data['low'].iloc[i] < data['low'].iloc[i+2]):
                
                strength = self.calculate_fenxing_strength(data, i, 'bottom')
                if strength >= self.parameters['fenxing_strength']:
                    fenxing.append({
                        'index': i,
                        'type': 'bottom',
                        'price': data['low'].iloc[i],
                        'strength': strength,
                        'date': data.index[i] if hasattr(data.index[i], 'date') else i
                    })
        
        return fenxing
    
    def calculate_fenxing_strength(self, data: pd.DataFrame, index: int, fenxing_type: str) -> float:
        """计算分型强度"""
        window = 10
        start = max(0, index - window)
        end = min(len(data), index + window + 1)
        
        if fenxing_type == 'top':
            current_high = data['high'].iloc[index]
            nearby_highs = data['high'].iloc[start:end]
            if nearby_highs.std() > 0:
                strength = (current_high - nearby_highs.mean()) / nearby_highs.std()
            else:
                strength = 1
        else:  # bottom
            current_low = data['low'].iloc[index]
            nearby_lows = data['low'].iloc[start:end]
            if nearby_lows.std() > 0:
                strength = (nearby_lows.mean() - current_low) / nearby_lows.std()
            else:
                strength = 1
        
        return max(1, strength)
    
    def construct_bi(self, data: pd.DataFrame, fenxing: List[Dict]) -> List[Dict]:
        """构造笔"""
        if len(fenxing) < 2:
            return []
        
        bi_lines = []
        i = 0
        
        while i < len(fenxing) - 1:
            start_point = fenxing[i]
            
            # 寻找下一个相反类型的分型
            j = i + 1
            while j < len(fenxing) and fenxing[j]['type'] == start_point['type']:
                # 如果是同类型分型，选择更极端的点
                if start_point['type'] == 'top':
                    if fenxing[j]['price'] > start_point['price']:
                        start_point = fenxing[j]
                        i = j
                else:  # bottom
                    if fenxing[j]['price'] < start_point['price']:
                        start_point = fenxing[j]
                        i = j
                j += 1
            
            if j < len(fenxing):
                end_point = fenxing[j]
                
                # 检查笔的有效性
                if self.is_valid_bi(data, start_point, end_point):
                    bi_lines.append({
                        'start': start_point,
                        'end': end_point,
                        'direction': 'up' if start_point['type'] == 'bottom' else 'down',
                        'strength': (start_point['strength'] + end_point['strength']) / 2,
                        'length': abs(end_point['price'] - start_point['price']),
                        'bars': end_point['index'] - start_point['index']
                    })
                
                i = j
            else:
                break
        
        return bi_lines
    
    def is_valid_bi(self, data: pd.DataFrame, start: Dict, end: Dict) -> bool:
        """判断笔的有效性"""
        # 1. K线数量要求
        if end['index'] - start['index'] < self.parameters['bi_min_bars']:
            return False
        
        # 2. 价格幅度要求
        price_range = abs(end['price'] - start['price'])
        avg_price = (start['price'] + end['price']) / 2
        if price_range / avg_price < 0.01:  # 至少1%的价格变化
            return False
        
        # 3. 中间不能有更极端的点
        start_idx, end_idx = start['index'], end['index']
        if start['type'] == 'top':  # 向下的笔
            max_high = data['high'].iloc[start_idx:end_idx+1].max()
            if max_high > start['price'] * 1.001:  # 允许0.1%的误差
                return False
        else:  # 向上的笔
            min_low = data['low'].iloc[start_idx:end_idx+1].min()
            if min_low < start['price'] * 0.999:  # 允许0.1%的误差
                return False
        
        return True
    
    def construct_xianduan(self, bi_lines: List[Dict]) -> List[Dict]:
        """构造线段"""
        if len(bi_lines) < self.parameters['xianduan_min_bi']:
            return []
        
        xianduan = []
        i = 0
        
        while i < len(bi_lines) - 2:
            # 寻找连续同方向的笔
            start_bi = bi_lines[i]
            current_direction = start_bi['direction']
            bi_group = [start_bi]
            
            j = i + 1
            while j < len(bi_lines):
                if bi_lines[j]['direction'] == current_direction:
                    bi_group.append(bi_lines[j])
                    j += 1
                else:
                    break
            
            # 如果有足够的同方向笔，构成线段
            if len(bi_group) >= self.parameters['xianduan_min_bi']:
                xianduan.append({
                    'start': bi_group[0]['start'],
                    'end': bi_group[-1]['end'],
                    'direction': current_direction,
                    'bi_count': len(bi_group),
                    'strength': sum([bi['strength'] for bi in bi_group]) / len(bi_group),
                    'total_length': sum([bi['length'] for bi in bi_group])
                })
            
            i = j if j > i + 1 else i + 1
        
        return xianduan
    
    def identify_zhongshu(self, xianduan: List[Dict]) -> List[Dict]:
        """识别中枢"""
        if len(xianduan) < 3:
            return []
        
        zhongshu = []
        
        for i in range(len(xianduan) - 2):
            xd1, xd2, xd3 = xianduan[i], xianduan[i+1], xianduan[i+2]
            
            # 计算重叠区间
            prices1 = [xd1['start']['price'], xd1['end']['price']]
            prices2 = [xd2['start']['price'], xd2['end']['price']]
            prices3 = [xd3['start']['price'], xd3['end']['price']]
            
            zone_high = min(max(prices1), max(prices2), max(prices3))
            zone_low = max(min(prices1), min(prices2), min(prices3))
            
            # 检查重叠有效性
            if zone_high > zone_low:
                overlap_ratio = (zone_high - zone_low) / (max(max(prices1), max(prices2), max(prices3)) - 
                                                         min(min(prices1), min(prices2), min(prices3)))
                
                if overlap_ratio >= self.parameters['zhongshu_min_overlap']:
                    zhongshu.append({
                        'start_index': xd1['start']['index'],
                        'end_index': xd3['end']['index'],
                        'zone_high': zone_high,
                        'zone_low': zone_low,
                        'zone_mid': (zone_high + zone_low) / 2,
                        'strength': (xd1['strength'] + xd2['strength'] + xd3['strength']) / 3,
                        'type': self.classify_zhongshu_type(xd1, xd2, xd3)
                    })
        
        return zhongshu
    
    def classify_zhongshu_type(self, xd1: Dict, xd2: Dict, xd3: Dict) -> str:
        """分类中枢类型"""
        # 根据进入和离开中枢的方向判断
        if xd1['direction'] == 'up' and xd3['direction'] == 'up':
            return 'upward'  # 向上中枢
        elif xd1['direction'] == 'down' and xd3['direction'] == 'down':
            return 'downward'  # 向下中枢
        else:
            return 'consolidation'  # 整理中枢
    
    def analyze_wave_structure(self, xianduan: List[Dict], zhongshu: List[Dict]) -> Dict:
        """分析波段结构"""
        if not xianduan:
            return {'trend': 'unknown', 'strength': 0, 'phase': 'unknown'}
        
        # 分析趋势方向
        recent_xianduan = xianduan[-min(5, len(xianduan)):]
        up_count = sum(1 for xd in recent_xianduan if xd['direction'] == 'up')
        down_count = len(recent_xianduan) - up_count
        
        if up_count > down_count:
            trend = 'uptrend'
        elif down_count > up_count:
            trend = 'downtrend'
        else:
            trend = 'sideways'
        
        # 计算趋势强度
        if recent_xianduan:
            avg_strength = sum([xd['strength'] for xd in recent_xianduan]) / len(recent_xianduan)
            strength = min(100, avg_strength * 20)  # 标准化到0-100
        else:
            strength = 0
        
        # 判断波段阶段
        if zhongshu:
            latest_zhongshu = zhongshu[-1]
            if latest_zhongshu['type'] == 'consolidation':
                phase = 'consolidation'
            elif trend == 'uptrend' and latest_zhongshu['type'] == 'upward':
                phase = 'continuation'
            elif trend == 'downtrend' and latest_zhongshu['type'] == 'downward':
                phase = 'continuation'
            else:
                phase = 'reversal'
        else:
            phase = 'trending'
        
        return {
            'trend': trend,
            'strength': strength,
            'phase': phase,
            'recent_xianduan_count': len(recent_xianduan)
        }
    
    def calculate_support_resistance(self, data: pd.DataFrame, fenxing: List[Dict]) -> Dict:
        """计算支撑阻力位"""
        if not fenxing:
            return {'support_levels': [], 'resistance_levels': []}
        
        # 提取顶部和底部分型
        tops = [f for f in fenxing if f['type'] == 'top']
        bottoms = [f for f in fenxing if f['type'] == 'bottom']
        
        # 计算阻力位（基于顶部分型）
        resistance_levels = []
        for top in tops[-10:]:  # 最近10个顶部
            resistance_levels.append({
                'price': top['price'],
                'strength': top['strength'],
                'index': top['index']
            })
        
        # 计算支撑位（基于底部分型）
        support_levels = []
        for bottom in bottoms[-10:]:  # 最近10个底部
            support_levels.append({
                'price': bottom['price'],
                'strength': bottom['strength'],
                'index': bottom['index']
            })
        
        # 按强度排序
        resistance_levels.sort(key=lambda x: x['strength'], reverse=True)
        support_levels.sort(key=lambda x: x['strength'], reverse=True)
        
        return {
            'resistance_levels': resistance_levels[:5],  # 取前5个最强阻力位
            'support_levels': support_levels[:5]         # 取前5个最强支撑位
        }
    
    def generate_trading_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 分析缠论结构
        chanlun_structure = self.analyze_chanlun_structure(data)
        
        # 初始化信号
        signals = [0] * len(data)
        
        # 遍历数据生成信号
        for i in range(50, len(data)):  # 从第50个数据点开始，确保有足够的历史数据
            current_price = data['close'].iloc[i]
            
            # 生成买入信号
            buy_signal = self.generate_buy_signal(data, i, chanlun_structure, current_price)
            
            # 生成卖出信号
            sell_signal = self.generate_sell_signal(data, i, chanlun_structure, current_price)
            
            if buy_signal:
                signals[i] = 1
            elif sell_signal:
                signals[i] = -1
        
        # 添加信号到数据
        data['signals'] = signals
        
        return data
    
    def generate_buy_signal(self, data: pd.DataFrame, index: int, structure: Dict, price: float) -> bool:
        """生成买入信号"""
        # 当前无持仓才考虑买入
        if self.current_position != 0:
            return False
        
        # 检查交易间隔
        if (self.trade_history and 
            index - self.trade_history[-1].get('exit_index', 0) < self.parameters['min_trade_interval']):
            return False
        
        buy_conditions = []
        
        # 1. 支撑位买入
        support_levels = structure['support_resistance']['support_levels']
        for support in support_levels[:3]:  # 检查前3个最强支撑位
            if abs(price - support['price']) / support['price'] < 0.02:  # 价格接近支撑位2%以内
                buy_conditions.append(True)
                break
        else:
            buy_conditions.append(False)
        
        # 2. 中枢下沿买入
        zhongshu = structure['zhongshu']
        if zhongshu:
            latest_zhongshu = zhongshu[-1]
            if (latest_zhongshu['end_index'] >= index - 10 and  # 最近的中枢
                price <= latest_zhongshu['zone_low'] * 1.01):   # 价格接近中枢下沿
                buy_conditions.append(True)
            else:
                buy_conditions.append(False)
        else:
            buy_conditions.append(False)
        
        # 3. 波段结构买入
        wave = structure['wave_structure']
        if (wave['trend'] == 'uptrend' and wave['strength'] > 30 and 
            wave['phase'] in ['continuation', 'trending']):
            buy_conditions.append(True)
        else:
            buy_conditions.append(False)
        
        # 4. 笔的买入信号
        bi_lines = structure['bi']
        if bi_lines:
            latest_bi = bi_lines[-1]
            if (latest_bi['direction'] == 'up' and 
                latest_bi['end']['index'] >= index - 5 and
                latest_bi['strength'] > 2.0):
                buy_conditions.append(True)
            else:
                buy_conditions.append(False)
        else:
            buy_conditions.append(False)
        
        # 5. 成交量确认
        if 'vol' in data.columns:
            recent_vol = data['vol'].iloc[max(0, index-5):index+1].mean()
            avg_vol = data['vol'].iloc[max(0, index-20):index].mean()
            if recent_vol > avg_vol * self.parameters['volume_confirm_ratio']:
                buy_conditions.append(True)
            else:
                buy_conditions.append(False)
        else:
            buy_conditions.append(True)  # 如果没有成交量数据，默认通过
        
        # 买入决策：至少满足3个条件
        return sum(buy_conditions) >= 3
    
    def generate_sell_signal(self, data: pd.DataFrame, index: int, structure: Dict, price: float) -> bool:
        """生成卖出信号"""
        # 当前有持仓才考虑卖出
        if self.current_position == 0:
            return False
        
        sell_conditions = []
        
        # 1. 止损止盈
        if self.entry_price > 0:
            # 更新最高价
            self.highest_price = max(self.highest_price, price)
            
            # 止损
            if price <= self.entry_price * (1 - self.parameters['stop_loss_ratio']):
                return True
            
            # 止盈
            if price >= self.entry_price * (1 + self.parameters['take_profit_ratio']):
                return True
            
            # 移动止损
            if price <= self.highest_price * (1 - self.parameters['trailing_stop_ratio']):
                return True
            
            # 最大持仓时间
            if index - self.entry_date >= self.parameters['max_hold_days']:
                return True
        
        # 2. 阻力位卖出
        resistance_levels = structure['support_resistance']['resistance_levels']
        for resistance in resistance_levels[:3]:
            if abs(price - resistance['price']) / resistance['price'] < 0.02:
                sell_conditions.append(True)
                break
        else:
            sell_conditions.append(False)
        
        # 3. 中枢上沿卖出
        zhongshu = structure['zhongshu']
        if zhongshu:
            latest_zhongshu = zhongshu[-1]
            if (latest_zhongshu['end_index'] >= index - 10 and
                price >= latest_zhongshu['zone_high'] * 0.99):
                sell_conditions.append(True)
            else:
                sell_conditions.append(False)
        else:
            sell_conditions.append(False)
        
        # 4. 波段结构卖出
        wave = structure['wave_structure']
        if (wave['trend'] == 'downtrend' and wave['strength'] > 30 and
            wave['phase'] in ['reversal', 'continuation']):
            sell_conditions.append(True)
        else:
            sell_conditions.append(False)
        
        # 5. 笔的卖出信号
        bi_lines = structure['bi']
        if bi_lines:
            latest_bi = bi_lines[-1]
            if (latest_bi['direction'] == 'down' and
                latest_bi['end']['index'] >= index - 5 and
                latest_bi['strength'] > 2.0):
                sell_conditions.append(True)
            else:
                sell_conditions.append(False)
        else:
            sell_conditions.append(False)
        
        # 卖出决策：满足2个条件即可
        return sum(sell_conditions) >= 2

# 将策略代码转换为模板格式
CHANLUN_HIGH_YIELD_STRATEGY = '''
# 缠论高收益策略
# 基于波段结构、中枢、支撑阻力位的精准交易策略

signals = [0] * len(data)

# 策略参数
fenxing_strength = 2.0
bi_min_bars = 5
volume_confirm_ratio = 1.5

# 计算技术指标
rsi = RSI(data['close'], 14)
ma_short = SMA(data['close'], 5)
ma_long = SMA(data['close'], 20)
dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)

# 识别关键价位
def find_key_levels(data, window=20):
    highs = []
    lows = []
    for i in range(window, len(data) - window):
        if data['high'].iloc[i] == data['high'].iloc[i-window:i+window+1].max():
            highs.append((i, data['high'].iloc[i]))
        if data['low'].iloc[i] == data['low'].iloc[i-window:i+window+1].min():
            lows.append((i, data['low'].iloc[i]))
    return highs, lows

resistance_levels, support_levels = find_key_levels(data)

# 生成交易信号
position = 0
entry_price = 0
highest_price = 0

for i in range(50, len(data)):
    current_price = data['close'].iloc[i]
    
    # 买入条件
    if position == 0:
        buy_conditions = []
        
        # 1. RSI超卖反弹
        if rsi.iloc[i] < 35 and rsi.iloc[i] > rsi.iloc[i-1]:
            buy_conditions.append(True)
        
        # 2. 均线支撑
        if current_price > ma_short.iloc[i] and ma_short.iloc[i] > ma_long.iloc[i]:
            buy_conditions.append(True)
        
        # 3. MACD金叉
        if dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1]:
            buy_conditions.append(True)
        
        # 4. 支撑位买入
        for _, support_price in support_levels[-5:]:
            if abs(current_price - support_price) / support_price < 0.02:
                buy_conditions.append(True)
                break
        
        # 5. 成交量确认
        if 'vol' in data.columns:
            recent_vol = data['vol'].iloc[max(0, i-5):i+1].mean()
            avg_vol = data['vol'].iloc[max(0, i-20):i].mean()
            if recent_vol > avg_vol * volume_confirm_ratio:
                buy_conditions.append(True)
        
        # 买入决策
        if len(buy_conditions) >= 3:
            signals[i] = 1
            position = 1
            entry_price = current_price
            highest_price = current_price
    
    # 卖出条件
    elif position == 1:
        # 更新最高价
        highest_price = max(highest_price, current_price)
        
        sell_conditions = []
        
        # 1. 止损止盈
        if current_price <= entry_price * 0.97:  # 3%止损
            signals[i] = -1
            position = 0
            continue
        elif current_price >= entry_price * 1.08:  # 8%止盈
            signals[i] = -1
            position = 0
            continue
        elif current_price <= highest_price * 0.98:  # 2%移动止损
            signals[i] = -1
            position = 0
            continue
        
        # 2. RSI超买
        if rsi.iloc[i] > 65:
            sell_conditions.append(True)
        
        # 3. 均线压力
        if current_price < ma_short.iloc[i] or ma_short.iloc[i] < ma_long.iloc[i]:
            sell_conditions.append(True)
        
        # 4. MACD死叉
        if dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1]:
            sell_conditions.append(True)
        
        # 5. 阻力位卖出
        for _, resistance_price in resistance_levels[-5:]:
            if abs(current_price - resistance_price) / resistance_price < 0.02:
                sell_conditions.append(True)
                break
        
        # 卖出决策
        if len(sell_conditions) >= 2:
            signals[i] = -1
            position = 0
'''

if __name__ == "__main__":
    print("缠论高收益策略已创建完成！")
    print("策略特点：")
    print("1. 基于缠论分析波段结构")
    print("2. 识别中枢和支撑阻力位")
    print("3. 多条件组合确认买卖点")
    print("4. 完善的风险控制机制")
    print("5. 可在股票看图软件中使用")
