#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义策略代码收集功能
"""

import pandas as pd
import numpy as np
from datetime import datetime
from 使用者监控 import UserMonitor, set_user_phone, monitor_backtest_completion

def create_test_backtest_results():
    """创建简单的测试回测结果"""
    return {
        'initial_capital': 100000,
        'final_value': 120000,
        'total_return': 0.20,
        'annual_return': 0.25,
        'volatility': 0.15,
        'sharpe_ratio': 1.2,
        'max_drawdown': -0.08,
        'total_trades': 15,
        'win_rate': 0.65
    }

def test_custom_strategy_collection():
    """测试自定义策略代码收集"""
    print("=" * 70)
    print("测试自定义策略代码收集功能")
    print("=" * 70)
    
    # 1. 设置用户信息
    print("\n1. 设置用户信息...")
    test_phone = "13987654321"
    set_user_phone(test_phone)
    print(f"设置手机号: {test_phone}")
    
    # 2. 创建不同类型的自定义策略代码
    strategies = {
        "简单移动平均策略": """
# 简单移动平均策略
data['MA5'] = data['close'].rolling(window=5).mean()
data['MA20'] = data['close'].rolling(window=20).mean()

# 生成交易信号
data['signal'] = 0
data.loc[data['MA5'] > data['MA20'], 'signal'] = 1  # 买入
data.loc[data['MA5'] < data['MA20'], 'signal'] = -1  # 卖出

data['position'] = data['signal'].shift(1).fillna(0)
""",
        
        "复合技术指标策略": """
# 复合技术指标策略
import numpy as np

# 计算RSI
def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

data['RSI'] = calculate_rsi(data['close'])

# 计算MACD
ema12 = data['close'].ewm(span=12).mean()
ema26 = data['close'].ewm(span=26).mean()
data['MACD'] = ema12 - ema26
data['MACD_signal'] = data['MACD'].ewm(span=9).mean()

# 布林带
data['BB_middle'] = data['close'].rolling(window=20).mean()
data['BB_std'] = data['close'].rolling(window=20).std()
data['BB_upper'] = data['BB_middle'] + (data['BB_std'] * 2)
data['BB_lower'] = data['BB_middle'] - (data['BB_std'] * 2)

# 复合信号
buy_condition = (
    (data['RSI'] < 30) &  # 超卖
    (data['MACD'] > data['MACD_signal']) &  # MACD金叉
    (data['close'] < data['BB_lower'])  # 价格在布林带下轨
)

sell_condition = (
    (data['RSI'] > 70) |  # 超买
    (data['MACD'] < data['MACD_signal']) |  # MACD死叉
    (data['close'] > data['BB_upper'])  # 价格在布林带上轨
)

data['signal'] = 0
data.loc[buy_condition, 'signal'] = 1
data.loc[sell_condition, 'signal'] = -1
data['position'] = data['signal'].shift(1).fillna(0)
""",
        
        "量价结合策略": """
# 量价结合策略
# 结合价格和成交量进行分析

# 计算成交量移动平均
data['volume_ma'] = data['volume'].rolling(window=10).mean()

# 计算价格变化率
data['price_change'] = data['close'].pct_change()

# 计算成交量比率
data['volume_ratio'] = data['volume'] / data['volume_ma']

# 价格突破信号
data['price_high'] = data['high'].rolling(window=20).max()
data['price_low'] = data['low'].rolling(window=20).min()

# 买入条件：价格突破新高且成交量放大
buy_condition = (
    (data['close'] > data['price_high'].shift(1)) &  # 突破前期高点
    (data['volume_ratio'] > 1.5) &  # 成交量放大
    (data['price_change'] > 0.02)  # 涨幅超过2%
)

# 卖出条件：价格跌破新低或成交量萎缩
sell_condition = (
    (data['close'] < data['price_low'].shift(1)) |  # 跌破前期低点
    (data['volume_ratio'] < 0.5) |  # 成交量萎缩
    (data['price_change'] < -0.03)  # 跌幅超过3%
)

data['signal'] = 0
data.loc[buy_condition, 'signal'] = 1
data.loc[sell_condition, 'signal'] = -1
data['position'] = data['signal'].shift(1).fillna(0)

print(f"策略信号统计:")
print(f"买入信号: {(data['signal'] == 1).sum()}")
print(f"卖出信号: {(data['signal'] == -1).sum()}")
"""
    }
    
    # 3. 测试每种策略的收集和上传
    monitor = UserMonitor()
    
    for strategy_name, strategy_code in strategies.items():
        print(f"\n{'='*50}")
        print(f"测试策略: {strategy_name}")
        print(f"{'='*50}")
        
        print(f"策略代码长度: {len(strategy_code)} 字符")
        print(f"策略代码前100字符: {strategy_code[:100].strip()}...")
        
        # 创建回测结果
        backtest_results = create_test_backtest_results()
        
        # 收集数据
        collected_data = monitor.collect_backtest_data(backtest_results, strategy_code)
        
        # 验证收集的数据
        print(f"\n收集结果验证:")
        print(f"  手机号: {collected_data.get('phone_number', '未找到')}")
        print(f"  策略代码长度: {len(collected_data.get('custom_strategy_code', ''))}")
        print(f"  时间戳: {collected_data.get('timestamp', '未找到')}")
        
        # 生成XLSX文件
        filename = f"测试_{strategy_name.replace(' ', '_')}_{datetime.now().strftime('%H%M%S')}.xlsx"
        filepath = monitor.create_xlsx_file(collected_data, filename)
        
        if filepath:
            print(f"  XLSX文件: {filepath}")
            
            # 验证XLSX内容
            try:
                import os
                file_size = os.path.getsize(filepath)
                print(f"  文件大小: {file_size} 字节")
                
                # 读取并验证自定义公式表
                df_strategy = pd.read_excel(filepath, sheet_name='自定义公式')
                saved_code = df_strategy.iloc[0]['自定义策略代码']
                saved_phone = df_strategy.iloc[0]['手机号']
                
                print(f"  保存的手机号: {saved_phone}")
                print(f"  保存的策略代码长度: {len(saved_code)}")
                print(f"  策略代码匹配: {'✅' if saved_code == strategy_code else '❌'}")
                
            except Exception as e:
                print(f"  验证XLSX文件失败: {e}")
        else:
            print(f"  ❌ XLSX文件生成失败")
    
    # 4. 测试完整监控流程
    print(f"\n{'='*70}")
    print("测试完整监控流程")
    print(f"{'='*70}")
    
    # 使用最复杂的策略进行完整测试
    complex_strategy = strategies["复合技术指标策略"]
    backtest_results = create_test_backtest_results()
    
    print(f"使用复合技术指标策略进行完整测试...")
    print(f"策略代码长度: {len(complex_strategy)} 字符")
    
    success = monitor_backtest_completion(backtest_results, complex_strategy)
    
    if success:
        print("✅ 完整监控流程测试成功")
        
        # 检查配置文件
        config = monitor.user_config
        if 'last_backtest' in config:
            last_backtest = config['last_backtest']
            print(f"最后回测记录:")
            print(f"  时间: {last_backtest.get('timestamp', '未知')}")
            print(f"  文件: {last_backtest.get('filename', '未知')}")
            print(f"  上传: {'成功' if last_backtest.get('upload_success', False) else '失败'}")
            print(f"  手机号: {last_backtest.get('phone_number', '未知')}")
    else:
        print("❌ 完整监控流程测试失败")
    
    print(f"\n{'='*70}")
    print("自定义策略代码收集功能测试完成")
    print(f"{'='*70}")

if __name__ == "__main__":
    test_custom_strategy_collection()
