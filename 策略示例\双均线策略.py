# 双均线交叉策略
# 当短期均线上穿长期均线时买入，下穿时卖出

signals = [0] * len(data)

# 计算移动平均线
short_period = 5  # 短期均线周期
long_period = 20  # 长期均线周期

short_ma = SMA(data['close'], short_period)
long_ma = SMA(data['close'], long_period)

for i in range(1, len(data)):
    if pd.notna(short_ma.iloc[i]) and pd.notna(long_ma.iloc[i]):
        # 金叉：短期均线上穿长期均线，买入
        if short_ma.iloc[i] > long_ma.iloc[i] and short_ma.iloc[i-1] <= long_ma.iloc[i-1]:
            signals[i] = 1
        # 死叉：短期均线下穿长期均线，卖出
        elif short_ma.iloc[i] < long_ma.iloc[i] and short_ma.iloc[i-1] >= long_ma.iloc[i-1]:
            signals[i] = -1
