"""
缠论优化策略 - 专注高年化收益率
基于缠论核心理念，结合技术指标，通过参数优化实现高收益率
"""

import pandas as pd
import numpy as np
from 技术指标库 import *
import itertools

class ChanlunOptimizedStrategy:
    """缠论优化策略"""
    
    def __init__(self):
        self.name = "缠论优化策略"
        
        # 优化后的参数
        self.params = {
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'ma_fast': 5,
            'ma_slow': 20,
            'ma_trend': 60,
            'stop_loss': 0.04,
            'take_profit': 0.10,
            'trailing_stop': 0.025,
            'volume_ratio': 1.3,
            'min_hold_days': 2,
            'max_hold_days': 20,
        }
        
    def find_support_resistance_levels(self, data, window=20, recent_count=10):
        """寻找支撑阻力位"""
        highs = []
        lows = []
        
        for i in range(window, len(data) - window):
            # 局部最高点
            if data['high'].iloc[i] == data['high'].iloc[i-window:i+window+1].max():
                highs.append((i, data['high'].iloc[i]))
            
            # 局部最低点
            if data['low'].iloc[i] == data['low'].iloc[i-window:i+window+1].min():
                lows.append((i, data['low'].iloc[i]))
        
        # 返回最近的支撑阻力位
        return lows[-recent_count:], highs[-recent_count:]
    
    def identify_trend_structure(self, data, index, window=20):
        """识别趋势结构"""
        if index < window:
            return 'unknown', 0
        
        # 计算价格动量
        recent_data = data.iloc[index-window:index+1]
        price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[0]) / recent_data['close'].iloc[0]
        
        # 计算趋势强度
        ma_short = recent_data['close'].rolling(5).mean()
        ma_long = recent_data['close'].rolling(15).mean()
        
        if len(ma_short) > 0 and len(ma_long) > 0:
            trend_strength = abs(ma_short.iloc[-1] - ma_long.iloc[-1]) / ma_long.iloc[-1] * 100
        else:
            trend_strength = 0
        
        # 判断趋势方向
        if price_change > 0.02 and trend_strength > 1:
            return 'uptrend', trend_strength
        elif price_change < -0.02 and trend_strength > 1:
            return 'downtrend', trend_strength
        else:
            return 'sideways', trend_strength
    
    def generate_signals(self, data):
        """生成交易信号"""
        signals = [0] * len(data)
        
        # 计算技术指标
        rsi = RSI(data['close'], self.params['rsi_period'])
        ma_fast = SMA(data['close'], self.params['ma_fast'])
        ma_slow = SMA(data['close'], self.params['ma_slow'])
        ma_trend = SMA(data['close'], self.params['ma_trend'])
        dif, dea, macd_hist = MACD(data['close'], 12, 26, 9)
        
        # 寻找支撑阻力位
        support_levels, resistance_levels = self.find_support_resistance_levels(data)
        
        # 状态变量
        position = 0
        entry_price = 0
        entry_index = 0
        highest_price = 0
        
        for i in range(60, len(data)):
            current_price = data['close'].iloc[i]
            
            # 买入逻辑
            if position == 0:
                buy_score = 0
                
                # 1. RSI超卖反弹
                if (pd.notna(rsi.iloc[i]) and rsi.iloc[i] < self.params['rsi_oversold'] and 
                    rsi.iloc[i] > rsi.iloc[i-1]):
                    buy_score += 2
                
                # 2. 均线多头排列
                if (pd.notna(ma_fast.iloc[i]) and pd.notna(ma_slow.iloc[i]) and
                    ma_fast.iloc[i] > ma_slow.iloc[i] and 
                    current_price > ma_fast.iloc[i]):
                    buy_score += 2
                
                # 3. MACD金叉
                if (pd.notna(dif.iloc[i]) and pd.notna(dea.iloc[i]) and
                    dif.iloc[i] > dea.iloc[i] and dif.iloc[i-1] <= dea.iloc[i-1]):
                    buy_score += 2
                
                # 4. 支撑位买入
                for _, support_price in support_levels:
                    if abs(current_price - support_price) / support_price < 0.025:
                        buy_score += 3
                        break
                
                # 5. 趋势确认
                trend, strength = self.identify_trend_structure(data, i)
                if trend == 'uptrend' and strength > 2:
                    buy_score += 2
                
                # 6. 成交量确认
                if 'vol' in data.columns and i >= 20:
                    recent_vol = data['vol'].iloc[max(0, i-5):i+1].mean()
                    avg_vol = data['vol'].iloc[max(0, i-20):i].mean()
                    if recent_vol > avg_vol * self.params['volume_ratio']:
                        buy_score += 1
                
                # 买入决策
                if buy_score >= 6:  # 需要较高的确信度
                    signals[i] = 1
                    position = 1
                    entry_price = current_price
                    entry_index = i
                    highest_price = current_price
            
            # 卖出逻辑
            elif position == 1:
                # 更新最高价
                highest_price = max(highest_price, current_price)
                
                sell_signal = False
                sell_reason = ""
                
                # 1. 止损
                if current_price <= entry_price * (1 - self.params['stop_loss']):
                    sell_signal = True
                    sell_reason = "止损"
                
                # 2. 止盈
                elif current_price >= entry_price * (1 + self.params['take_profit']):
                    sell_signal = True
                    sell_reason = "止盈"
                
                # 3. 移动止损
                elif current_price <= highest_price * (1 - self.params['trailing_stop']):
                    sell_signal = True
                    sell_reason = "移动止损"
                
                # 4. 最大持仓时间
                elif i - entry_index >= self.params['max_hold_days']:
                    sell_signal = True
                    sell_reason = "超时"
                
                # 5. 技术指标卖出
                else:
                    sell_score = 0
                    
                    # RSI超买
                    if pd.notna(rsi.iloc[i]) and rsi.iloc[i] > self.params['rsi_overbought']:
                        sell_score += 2
                    
                    # 均线死叉
                    if (pd.notna(ma_fast.iloc[i]) and pd.notna(ma_slow.iloc[i]) and
                        ma_fast.iloc[i] < ma_slow.iloc[i]):
                        sell_score += 2
                    
                    # MACD死叉
                    if (pd.notna(dif.iloc[i]) and pd.notna(dea.iloc[i]) and
                        dif.iloc[i] < dea.iloc[i] and dif.iloc[i-1] >= dea.iloc[i-1]):
                        sell_score += 2
                    
                    # 阻力位卖出
                    for _, resistance_price in resistance_levels:
                        if abs(current_price - resistance_price) / resistance_price < 0.025:
                            sell_score += 3
                            break
                    
                    # 趋势转弱
                    trend, strength = self.identify_trend_structure(data, i)
                    if trend == 'downtrend' and strength > 2:
                        sell_score += 2
                    
                    if sell_score >= 4:  # 卖出确信度
                        sell_signal = True
                        sell_reason = "技术卖出"
                
                if sell_signal:
                    signals[i] = -1
                    position = 0
        
        return signals
    
    def backtest(self, data):
        """回测策略"""
        signals = self.generate_signals(data)
        
        # 计算交易结果
        trades = []
        position = 0
        entry_price = 0
        entry_index = 0
        
        for i in range(len(signals)):
            if signals[i] == 1 and position == 0:
                position = 1
                entry_price = data['close'].iloc[i]
                entry_index = i
            elif signals[i] == -1 and position == 1:
                exit_price = data['close'].iloc[i]
                trade_return = (exit_price - entry_price) / entry_price
                hold_days = i - entry_index
                
                trades.append({
                    'entry_index': entry_index,
                    'exit_index': i,
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'return': trade_return,
                    'hold_days': hold_days
                })
                position = 0
        
        # 计算统计指标
        if trades:
            total_return = sum([t['return'] for t in trades])
            win_trades = [t for t in trades if t['return'] > 0]
            win_rate = len(win_trades) / len(trades)
            avg_return = total_return / len(trades)
            avg_hold_days = sum([t['hold_days'] for t in trades]) / len(trades)
            
            # 计算年化收益率
            if 'trade_date' in data.columns:
                try:
                    start_date = pd.to_datetime(str(data['trade_date'].iloc[0]))
                    end_date = pd.to_datetime(str(data['trade_date'].iloc[-1]))
                    years = (end_date - start_date).days / 365.25
                    if years > 0:
                        annual_return = (1 + total_return) ** (1/years) - 1
                    else:
                        annual_return = 0
                except:
                    # 备用计算方法
                    years = len(data) / 252  # 假设252个交易日
                    annual_return = total_return / years if years > 0 else 0
            else:
                years = len(data) / 252
                annual_return = total_return / years if years > 0 else 0
        else:
            total_return = 0
            annual_return = 0
            win_rate = 0
            avg_return = 0
            avg_hold_days = 0
        
        return {
            'signals': signals,
            'trades': trades,
            'total_return': total_return,
            'annual_return': annual_return,
            'win_rate': win_rate,
            'total_trades': len(trades),
            'avg_return': avg_return,
            'avg_hold_days': avg_hold_days
        }

def optimize_strategy_parameters(data, max_iterations=50):
    """优化策略参数"""
    print("开始参数优化...")
    
    # 参数优化范围
    param_ranges = {
        'rsi_oversold': [25, 30, 35],
        'rsi_overbought': [65, 70, 75],
        'stop_loss': [0.03, 0.04, 0.05],
        'take_profit': [0.08, 0.10, 0.12],
        'trailing_stop': [0.02, 0.025, 0.03]
    }
    
    best_annual_return = -999
    best_params = None
    results_history = []
    
    # 网格搜索
    param_combinations = list(itertools.product(*param_ranges.values()))
    param_names = list(param_ranges.keys())
    
    for i, values in enumerate(param_combinations[:max_iterations]):
        # 创建参数字典
        test_params = dict(zip(param_names, values))
        
        # 测试参数
        strategy = ChanlunOptimizedStrategy()
        for param, value in test_params.items():
            strategy.params[param] = value
        
        try:
            results = strategy.backtest(data)
            annual_return = results['annual_return']
            
            results_history.append({
                'params': test_params.copy(),
                'annual_return': annual_return,
                'total_trades': results['total_trades'],
                'win_rate': results['win_rate']
            })
            
            if annual_return > best_annual_return:
                best_annual_return = annual_return
                best_params = test_params.copy()
                print(f"迭代 {i}: 发现更好参数，年化收益率: {annual_return:.2%}")
        
        except Exception as e:
            print(f"迭代 {i} 失败: {e}")
            continue
    
    print(f"\n优化完成！最佳年化收益率: {best_annual_return:.2%}")
    print(f"最佳参数: {best_params}")
    
    return best_params, best_annual_return, results_history

def run_chanlun_strategy():
    """运行缠论策略"""
    try:
        # 加载数据
        df = pd.read_excel('梦百合.xlsx')
        df = df.sort_values('trade_date').reset_index(drop=True)
        
        if '价格' in df.columns:
            df['close'] = df['价格']
        
        for col in ['open', 'high', 'low', 'close', 'vol']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna(subset=['close'])
        
        print(f"数据加载成功，共{len(df)}条记录")
        print(f"数据时间范围: {df['trade_date'].min()} - {df['trade_date'].max()}")
        
        # 选择模式
        mode = input("选择模式 (1: 基础测试, 2: 参数优化): ").strip()
        
        if mode == "2":
            # 参数优化
            best_params, best_return, history = optimize_strategy_parameters(df)
            
            # 使用最佳参数运行最终测试
            strategy = ChanlunOptimizedStrategy()
            for param, value in best_params.items():
                strategy.params[param] = value
            
            final_results = strategy.backtest(df)
            
            print(f"\n=== 最终优化结果 ===")
            print(f"年化收益率: {final_results['annual_return']:.2%}")
            print(f"总收益率: {final_results['total_return']:.2%}")
            print(f"交易次数: {final_results['total_trades']}")
            print(f"胜率: {final_results['win_rate']:.2%}")
            print(f"平均每笔收益: {final_results['avg_return']:.2%}")
            print(f"平均持仓天数: {final_results['avg_hold_days']:.1f}")
            
            if final_results['annual_return'] > 0.02:
                print(f"\n🎉 策略优化成功！年化收益率达到 {final_results['annual_return']:.2%}")
            else:
                print(f"\n⚠️  策略需要进一步优化")
        
        else:
            # 基础测试
            strategy = ChanlunOptimizedStrategy()
            results = strategy.backtest(df)
            
            print(f"\n=== 缠论策略回测结果 ===")
            print(f"年化收益率: {results['annual_return']:.2%}")
            print(f"总收益率: {results['total_return']:.2%}")
            print(f"交易次数: {results['total_trades']}")
            print(f"胜率: {results['win_rate']:.2%}")
            print(f"平均每笔收益: {results['avg_return']:.2%}")
            print(f"平均持仓天数: {results['avg_hold_days']:.1f}")
            
            if results['annual_return'] > 0.02:
                print(f"\n✅ 策略达到目标！")
            else:
                print(f"\n⚠️  建议运行参数优化")
        
    except Exception as e:
        print(f"策略运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_chanlun_strategy()
